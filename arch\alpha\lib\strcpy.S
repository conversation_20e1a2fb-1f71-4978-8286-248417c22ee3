/* SPDX-License-Identifier: GPL-2.0 */
/*
 * arch/alpha/lib/strcpy.S
 * Contributed by <PERSON> (<EMAIL>)
 *
 * Copy a null-terminated string from SRC to DST.  Return a pointer
 * to the null-terminator in the source.
 */
#include <linux/export.h>
	.text

	.align 3
	.globl strcpy
	.ent strcpy
strcpy:
	.frame $30, 0, $26
	.prologue 0

	mov	$16, $0		# set up return value
	mov	$26, $23	# set up return address
	unop
	br	__stxcpy	# do the copy

	.end strcpy
	EXPORT_SYMBOL(strcpy)
