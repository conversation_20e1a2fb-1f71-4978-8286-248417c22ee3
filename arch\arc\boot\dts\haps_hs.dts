// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2016-2014 Synopsys, Inc. (www.synopsys.com)
 */
/dts-v1/;

/include/ "skeleton_hs.dtsi"

/ {
	model = "snps,zebu_hs";
	compatible = "snps,zebu_hs";
	#address-cells = <2>;
	#size-cells = <2>;
	interrupt-parent = <&core_intc>;

	memory {
		device_type = "memory";
		/* CONFIG_LINUX_RAM_BASE needs to match low mem start */
		reg = <0x0 0x80000000 0x0 0x40000000	/* 1 GB low mem */
		       0x1 0x00000000 0x0 0x40000000>;	/* 1 GB highmem */
	};

	chosen {
		bootargs = "earlycon=uart8250,mmio32,0xf0000000,115200n8 console=ttyS0,115200n8 debug print-fatal-signals=1";
	};

	aliases {
		serial0 = &uart0;
	};

	fpga {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		/* only perip space at end of low mem accessible
			  bus addr,  parent bus addr, size    */
		ranges = <0x80000000 0x0 0x80000000 0x80000000>;

		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <50000000>;
		};

		core_intc: interrupt-controller {
			compatible = "snps,archs-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		uart0: serial@f0000000 {
			compatible = "ns16550a";
			reg = <0xf0000000 0x2000>;
			interrupts = <24>;
			clock-frequency = <50000000>;
			baud = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			no-loopback-test = <1>;
		};

		arcpct0: pct {
			compatible = "snps,archs-pct";
			#interrupt-cells = <1>;
			interrupts = <20>;
		};

		virtio0: virtio@f0100000 {
			compatible = "virtio,mmio";
			reg = <0xf0100000 0x2000>;
			interrupts = <31>;
		};

		virtio1: virtio@f0102000 {
			compatible = "virtio,mmio";
			reg = <0xf0102000 0x2000>;
			interrupts = <32>;
		};

		virtio2: virtio@f0104000 {
			compatible = "virtio,mmio";
			reg = <0xf0104000 0x2000>;
			interrupts = <33>;
		};

		virtio3: virtio@f0106000 {
			compatible = "virtio,mmio";
			reg = <0xf0106000 0x2000>;
			interrupts = <34>;
		};

		virtio4: virtio@f0108000 {
			compatible = "virtio,mmio";
			reg = <0xf0108000 0x2000>;
			interrupts = <35>;
		};
	};
};
