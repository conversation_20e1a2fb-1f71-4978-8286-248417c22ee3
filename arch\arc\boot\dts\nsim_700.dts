// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2012 Synopsys, Inc. (www.synopsys.com)
 */
/dts-v1/;

/include/ "skeleton.dtsi"

/ {
	model = "snps,nsim";
	compatible = "snps,nsim";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&core_intc>;

	chosen {
		bootargs = "earlycon=uart8250,mmio32,0xf0000000,115200n8 console=ttyS0,115200n8 print-fatal-signals=1";
	};

	aliases {
		serial0 = &uart0;
	};

	fpga {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		/* child and parent address space 1:1 mapped */
		ranges;

		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <80000000>;
		};

		core_intc: interrupt-controller {
			compatible = "snps,arc700-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		uart0: serial@f0000000 {
			compatible = "ns16550a";
			reg = <0xf0000000 0x2000>;
			interrupts = <24>;
			clock-frequency = <50000000>;
			baud = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			no-loopback-test = <1>;
		};

		arcpct0: pct {
			compatible = "snps,arc700-pct";
		};
	};
};
