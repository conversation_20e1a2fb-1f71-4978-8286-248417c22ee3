// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Cubietech CubieBoard6
 *
 * Copyright (c) 2017 <PERSON>
 */

/dts-v1/;

#include "owl-s500.dtsi"

/ {
	compatible = "cubietech,cubieboard6", "actions,s500";
	model = "CubieBoard6";

	aliases {
		serial3 = &uart3;
	};

	chosen {
		stdout-path = "serial3:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x80000000>;
	};
};

&timer {
	clocks = <&hosc>;
};

&uart3 {
	status = "okay";
};
