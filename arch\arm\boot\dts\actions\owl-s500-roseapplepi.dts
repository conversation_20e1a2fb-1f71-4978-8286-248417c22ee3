// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Roseapple Pi
 *
 * Copyright (C) 2020-2021 C<PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "owl-s500.dtsi"

/ {
	compatible = "roseapplepi,roseapplepi", "actions,s500";
	model = "Roseapple Pi";

	aliases {
		mmc0 = &mmc0;
		serial2 = &uart2;
	};

	chosen {
		stdout-path = "serial2:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x80000000>; /* 2GB */
	};

	syspwr: regulator-5v0 {
		compatible = "regulator-fixed";
		regulator-name = "SYSPWR";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};
};

&cpu0 {
	cpu0-supply = <&vdd_cpu>;
};

&i2c0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0_pins>;

	atc260x: pmic@65 {
		compatible = "actions,atc2603c";
		reg = <0x65>;
		interrupt-parent = <&sirq>;
		interrupts = <2 IRQ_TYPE_LEVEL_HIGH>;

		reset-time-sec = <6>;

		regulators {
			compatible = "actions,atc2603c-regulator";

			dcdc1-supply = <&syspwr>;
			dcdc2-supply = <&syspwr>;
			dcdc3-supply = <&syspwr>;
			ldo1-supply = <&syspwr>;
			ldo2-supply = <&syspwr>;
			ldo3-supply = <&syspwr>;
			ldo5-supply = <&syspwr>;
			ldo6-supply = <&syspwr>;
			ldo7-supply = <&syspwr>;
			ldo8-supply = <&syspwr>;
			ldo11-supply = <&syspwr>;
			ldo12-supply = <&syspwr>;
			switchldo1-supply = <&vcc>;

			vdd_cpu: dcdc1 {
				regulator-name = "VDD_CPU";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
			};

			vddq: dcdc2 {
				regulator-name = "VDDQ";
				regulator-min-microvolt = <1300000>;
				regulator-max-microvolt = <2150000>;
				regulator-always-on;
				regulator-boot-on;
			};

			vcc: dcdc3 {
				regulator-name = "VCC";
				regulator-min-microvolt = <2600000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vcc_3v3: ldo1 {
				regulator-name = "VCC_3V3";
				regulator-min-microvolt = <2600000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			avcc: ldo2 {
				regulator-name = "AVCC";
				regulator-min-microvolt = <2600000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			vdd_1v8: ldo3 {
				regulator-name = "VDD_1V8";
				regulator-min-microvolt = <1500000>;
				regulator-max-microvolt = <2000000>;
				regulator-always-on;
			};

			vcc_3v1: ldo5 {
				regulator-name = "VCC_3V1";
				regulator-min-microvolt = <2600000>;
				regulator-max-microvolt = <3300000>;
			};

			avdd: ldo6 {
				regulator-name = "AVDD";
				regulator-min-microvolt = <700000>;
				regulator-max-microvolt = <1400000>;
				regulator-always-on;
			};

			sens_1v8: ldo7 {
				regulator-name = "SENS_1V8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
			};

			ldo8: ldo8 {
				regulator-name = "LDO8";
				regulator-min-microvolt = <2300000>;
				regulator-max-microvolt = <3300000>;
			};

			svcc: ldo11 {
				regulator-name = "SVCC";
				regulator-min-microvolt = <2600000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
			};

			rtc_vdd: ldo12 {
				regulator-name = "RTC_VDD";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-always-on;
			};

			sd_vcc: switchldo1 {
				regulator-name = "SD_VCC";
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3300000>;
				regulator-always-on;
				regulator-boot-on;
			};
		};
	};
};

&i2c1 {
	status = "disabled";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pins>;
};

&i2c2 {
	status = "disabled";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2_pins>;
};

&pinctrl {
	i2c0_pins: i2c0-pins {
		pinmux {
			groups = "i2c0_mfp";
			function = "i2c0";
		};

		pinconf {
			pins = "i2c0_sclk", "i2c0_sdata";
			bias-pull-up;
		};
	};

	i2c1_pins: i2c1-pins {
		pinconf {
			pins = "i2c1_sclk", "i2c1_sdata";
			bias-pull-up;
		};
	};

	i2c2_pins: i2c2-pins {
		pinconf {
			pins = "i2c2_sclk", "i2c2_sdata";
			bias-pull-up;
		};
	};

	mmc0_pins: mmc0-pins {
		pinmux {
			groups = "sd0_d0_mfp", "sd0_d1_mfp", "sd0_d2_d3_mfp",
				 "sd0_cmd_mfp", "sd0_clk_mfp";
			function = "sd0";
		};

		drv-pinconf {
			groups = "sd0_d0_d3_drv", "sd0_cmd_drv", "sd0_clk_drv";
			drive-strength = <8>;
		};

		bias0-pinconf {
			pins = "sd0_d0", "sd0_d1", "sd0_d2",
			       "sd0_d3", "sd0_cmd";
			bias-pull-up;
		};

		bias1-pinconf {
			pins = "sd0_clk";
			bias-pull-down;
		};
	};

	ethernet_pins: ethernet-pins {
		eth_rmii-pinmux {
			groups = "rmii_txd0_mfp", "rmii_txd1_mfp",
				 "rmii_rxd0_mfp", "rmii_rxd1_mfp",
				 "rmii_txen_mfp", "rmii_rxen_mfp",
				 "rmii_crs_dv_mfp", "rmii_ref_clk_mfp";
			function = "eth_rmii";
		};

		phy_clk-pinmux {
			groups = "clko_25m_mfp";
			function = "clko_25m";
		};

		ref_clk-pinconf {
			groups = "rmii_ref_clk_drv";
			drive-strength = <2>;
		};

	};
};

/* uSD */
&mmc0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&mmc0_pins>;
	no-sdio;
	no-mmc;
	no-1-8-v;
	cd-gpios = <&pinctrl 117 GPIO_ACTIVE_LOW>;
	bus-width = <4>;
	vmmc-supply = <&sd_vcc>;
	vqmmc-supply = <&sd_vcc>;
};

&ethernet {
	pinctrl-names = "default";
	pinctrl-0 = <&ethernet_pins>;
	phy-mode = "rmii";
	phy-handle = <&eth_phy>;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;

		reset-gpios = <&pinctrl 88 GPIO_ACTIVE_LOW>; /* GPIOC24 */
		reset-delay-us = <10000>;
		reset-post-delay-us = <150000>;

		eth_phy: ethernet-phy@3 {
			reg = <0x3>;
			max-speed = <100>;
			interrupt-parent = <&sirq>;
			interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
		};
	};
};

&twd_timer {
	status = "okay";
};

&timer {
	clocks = <&hosc>;
};

&uart2 {
	status = "okay";
};
