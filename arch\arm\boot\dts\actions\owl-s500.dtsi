// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Actions Semi S500 SoC
 *
 * Copyright (c) 2016-2017 <PERSON>
 */

#include <dt-bindings/clock/actions,s500-cmu.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/power/owl-s500-powergate.h>
#include <dt-bindings/reset/actions,s500-reset.h>

/ {
	compatible = "actions,s500";
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
	};

	chosen {
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x0>;
			enable-method = "actions,s500-smp";
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x1>;
			enable-method = "actions,s500-smp";
		};

		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x2>;
			enable-method = "actions,s500-smp";
			power-domains = <&sps S500_PD_CPU2>;
		};

		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x3>;
			enable-method = "actions,s500-smp";
			power-domains = <&sps S500_PD_CPU3>;
		};
	};

	arm-pmu {
		compatible = "arm,cortex-a9-pmu";
		interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
		             <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
		             <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
		             <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	hosc: hosc {
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
		#clock-cells = <0>;
	};

	losc: losc {
		compatible = "fixed-clock";
		clock-frequency = <32768>;
		#clock-cells = <0>;
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		scu: scu@b0020000 {
			compatible = "arm,cortex-a9-scu";
			reg = <0xb0020000 0x100>;
		};

		global_timer: timer@b0020200 {
			compatible = "arm,cortex-a9-global-timer";
			reg = <0xb0020200 0x100>;
			interrupts = <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
			status = "disabled";
		};

		twd_timer: timer@b0020600 {
			compatible = "arm,cortex-a9-twd-timer";
			reg = <0xb0020600 0x20>;
			interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
			status = "disabled";
		};

		twd_wdt: wdt@b0020620 {
			compatible = "arm,cortex-a9-twd-wdt";
			reg = <0xb0020620 0xe0>;
			interrupts = <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_EDGE_RISING)>;
			status = "disabled";
		};

		gic: interrupt-controller@b0021000 {
			compatible = "arm,cortex-a9-gic";
			reg = <0xb0021000 0x1000>,
			      <0xb0020100 0x0100>;
			interrupt-controller;
			#interrupt-cells = <3>;
		};

		l2: cache-controller@b0022000 {
			compatible = "arm,pl310-cache";
			reg = <0xb0022000 0x1000>;
			cache-unified;
			cache-level = <2>;
			interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
			arm,tag-latency = <3 3 2>;
			arm,data-latency = <5 3 3>;
		};

		uart0: serial@b0120000 {
			compatible = "actions,s500-uart", "actions,owl-uart";
			reg = <0xb0120000 0x2000>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART0>;
			status = "disabled";
		};

		uart1: serial@b0122000 {
			compatible = "actions,s500-uart", "actions,owl-uart";
			reg = <0xb0122000 0x2000>;
			interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART1>;
			status = "disabled";
		};

		uart2: serial@b0124000 {
			compatible = "actions,s500-uart", "actions,owl-uart";
			reg = <0xb0124000 0x2000>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART2>;
			status = "disabled";
		};

		uart3: serial@b0126000 {
			compatible = "actions,s500-uart", "actions,owl-uart";
			reg = <0xb0126000 0x2000>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART3>;
			status = "disabled";
		};

		uart4: serial@b0128000 {
			compatible = "actions,s500-uart", "actions,owl-uart";
			reg = <0xb0128000 0x2000>;
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART4>;
			status = "disabled";
		};

		uart5: serial@b012a000 {
			compatible = "actions,s500-uart", "actions,owl-uart";
			reg = <0xb012a000 0x2000>;
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART5>;
			status = "disabled";
		};

		uart6: serial@b012c000 {
			compatible = "actions,s500-uart", "actions,owl-uart";
			reg = <0xb012c000 0x2000>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_UART6>;
			status = "disabled";
		};

		cmu: clock-controller@b0160000 {
			compatible = "actions,s500-cmu";
			reg = <0xb0160000 0x8000>;
			clocks = <&hosc>, <&losc>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		i2c0: i2c@b0170000 {
			compatible = "actions,s500-i2c";
			reg = <0xb0170000 0x4000>;
			clocks = <&cmu CLK_I2C0>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c1: i2c@b0174000 {
			compatible = "actions,s500-i2c";
			reg = <0xb0174000 0x4000>;
			clocks = <&cmu CLK_I2C1>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c2: i2c@b0178000 {
			compatible = "actions,s500-i2c";
			reg = <0xb0178000 0x4000>;
			clocks = <&cmu CLK_I2C2>;
			interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		i2c3: i2c@b017c000 {
			compatible = "actions,s500-i2c";
			reg = <0xb017c000 0x4000>;
			clocks = <&cmu CLK_I2C3>;
			interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";
		};

		sirq: interrupt-controller@b01b0200 {
			compatible = "actions,s500-sirq";
			reg = <0xb01b0200 0x4>;
			interrupt-controller;
			#interrupt-cells = <2>;
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>, /* SIRQ0 */
				     <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>, /* SIRQ1 */
				     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>; /* SIRQ2 */
		};

		timer: timer@b0168000 {
			compatible = "actions,s500-timer";
			reg = <0xb0168000 0x8000>;
			interrupts = <GIC_SPI  8 IRQ_TYPE_LEVEL_HIGH>,
			             <GIC_SPI  9 IRQ_TYPE_LEVEL_HIGH>,
			             <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
			             <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "2hz0", "2hz1", "timer0", "timer1";
		};

		sps: power-controller@b01b0100 {
			compatible = "actions,s500-sps";
			reg = <0xb01b0100 0x100>;
			#power-domain-cells = <1>;
		};

		pinctrl: pinctrl@b01b0000 {
			compatible = "actions,s500-pinctrl";
			reg = <0xb01b0000 0x40>, /* GPIO */
			      <0xb01b0040 0x10>, /* Multiplexing Control */
			      <0xb01b0060 0x18>, /* PAD Control */
			      <0xb01b0080 0xc>;  /* PAD Drive Capacity */
			clocks = <&cmu CLK_GPIO>;
			gpio-controller;
			gpio-ranges = <&pinctrl 0 0 132>;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>, /* GPIOA */
				     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>, /* GPIOB */
				     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>, /* GPIOC */
				     <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>, /* GPIOD */
				     <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>; /* GPIOE */
		};

		dma: dma-controller@b0260000 {
			compatible = "actions,s500-dma";
			reg = <0xb0260000 0xd00>;
			interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			#dma-cells = <1>;
			dma-channels = <12>;
			dma-requests = <46>;
			clocks = <&cmu CLK_DMAC>;
			power-domains = <&sps S500_PD_DMA>;
		};

		mmc0: mmc@b0230000 {
			compatible = "actions,s500-mmc", "actions,owl-mmc";
			reg = <0xb0230000 0x38>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_SD0>;
			resets = <&cmu RESET_SD0>;
			dmas = <&dma 2>;
			dma-names = "mmc";
			status = "disabled";
		};

		mmc1: mmc@b0234000 {
			compatible = "actions,s500-mmc", "actions,owl-mmc";
			reg = <0xb0234000 0x38>;
			interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_SD1>;
			resets = <&cmu RESET_SD1>;
			dmas = <&dma 3>;
			dma-names = "mmc";
			status = "disabled";
		};

		mmc2: mmc@b0238000 {
			compatible = "actions,s500-mmc", "actions,owl-mmc";
			reg = <0xb0238000 0x38>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_SD2>;
			resets = <&cmu RESET_SD2>;
			dmas = <&dma 4>;
			dma-names = "mmc";
			status = "disabled";
		};

		ethernet: ethernet@b0310000 {
			compatible = "actions,s500-emac", "actions,owl-emac";
			reg = <0xb0310000 0x10000>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cmu CLK_ETHERNET>, <&cmu CLK_RMII_REF>;
			clock-names = "eth", "rmii";
			resets = <&cmu RESET_ETHERNET>;
			status = "disabled";
		};
	};
};
