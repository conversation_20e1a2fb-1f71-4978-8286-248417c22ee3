// SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
/dts-v1/;

/* Bootloader installs ATF here */
/memreserve/ 0x80000000 0x200000;

#include "en7523.dtsi"

/ {
	model = "Airoha EN7523 Evaluation Board";
	compatible = "airoha,en7523-evb", "airoha,en7523";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		bootargs = "console=ttyS0,115200 earlycon";
		stdout-path = "serial0:115200n8";
		linux,usable-memory-range = <0x80200000 0x1fe00000>;
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>;
	};
};

&gpio0 {
	status = "okay";
};

&gpio1 {
	status = "okay";
};

&pcie0 {
	status = "okay";
};

&pcie1 {
	status = "okay";
};
