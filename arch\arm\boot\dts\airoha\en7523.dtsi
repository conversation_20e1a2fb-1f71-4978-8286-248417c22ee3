// SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/clock/en7523-clk.h>

/ {
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		npu_binary@84000000 {
			no-map;
			reg = <0x84000000 0xA00000>;
		};

		npu_flag@84B0000 {
			no-map;
			reg = <0x84B00000 0x100000>;
		};

		npu_pkt@85000000 {
			no-map;
			reg = <0x85000000 0x1A00000>;
		};

		npu_phyaddr@86B00000 {
			no-map;
			reg = <0x86B00000 0x100000>;
		};

		npu_rxdesc@86D00000 {
			no-map;
			reg = <0x86D00000 0x100000>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&cpu0>;
				};
				core1 {
					cpu = <&cpu1>;
				};
			};
		};

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x0>;
			enable-method = "psci";
			clock-frequency = <80000000>;
			next-level-cache = <&L2_0>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x1>;
			enable-method = "psci";
			clock-frequency = <80000000>;
			next-level-cache = <&L2_0>;
		};

		L2_0: l2-cache0 {
			compatible = "cache";
			cache-level = <2>;
			cache-unified;
		};
	};

	scu: system-controller@1fa20000 {
		compatible = "airoha,en7523-scu";
		reg = <0x1fa20000 0x400>,
		      <0x1fb00000 0x1000>;
		#clock-cells = <1>;
	};

	gic: interrupt-controller@9000000 {
		compatible = "arm,gic-v3";
		interrupt-controller;
		#interrupt-cells = <3>;
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0x09000000 0x20000>,
		      <0x09080000 0x80000>,
		      <0x09400000 0x2000>,
		      <0x09500000 0x2000>,
		      <0x09600000 0x20000>;
		interrupts = <GIC_PPI 9 IRQ_TYPE_LEVEL_LOW>;
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupt-parent = <&gic>;
		interrupts = <GIC_PPI 13 IRQ_TYPE_LEVEL_LOW>,
			     <GIC_PPI 14 IRQ_TYPE_LEVEL_LOW>,
			     <GIC_PPI 11 IRQ_TYPE_LEVEL_LOW>,
			     <GIC_PPI 10 IRQ_TYPE_LEVEL_LOW>;
	};

	uart1: serial@1fbf0000 {
		compatible = "ns16550";
		reg = <0x1fbf0000 0x30>;
		reg-io-width = <4>;
		reg-shift = <2>;
		interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <1843200>;
		status = "okay";
	};

	gpio0: gpio@1fbf0200 {
		compatible = "airoha,en7523-gpio";
		reg = <0x1fbf0204 0x4>,
		      <0x1fbf0200 0x4>,
		      <0x1fbf0220 0x4>,
		      <0x1fbf0214 0x4>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio1: gpio@1fbf0270 {
		compatible = "airoha,en7523-gpio";
		reg = <0x1fbf0270 0x4>,
		      <0x1fbf0260 0x4>,
		      <0x1fbf0264 0x4>,
		      <0x1fbf0278 0x4>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	pcie0: pcie@1fa91000 {
		compatible = "airoha,en7523-pcie", "mediatek,mt7622-pcie";
		device_type = "pci";
		reg = <0x1fa91000 0x1000>;
		reg-names = "port0";
		linux,pci-domain = <0>;
		#address-cells = <3>;
		#size-cells = <2>;
		interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "pcie_irq";
		clocks = <&scu EN7523_CLK_PCIE>;
		clock-names = "sys_ck0";
		bus-range = <0x00 0xff>;
		ranges = <0x82000000 0 0x20000000  0x20000000  0 0x8000000>;
		status = "disabled";

		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pcie_intc0 0>,
				<0 0 0 2 &pcie_intc0 1>,
				<0 0 0 3 &pcie_intc0 2>,
				<0 0 0 4 &pcie_intc0 3>;
		pcie_intc0: interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
		};
	};

	pcie1: pcie@1fa92000 {
		compatible = "airoha,en7523-pcie", "mediatek,mt7622-pcie";
		device_type = "pci";
		reg = <0x1fa92000 0x1000>;
		reg-names = "port1";
		linux,pci-domain = <1>;
		#address-cells = <3>;
		#size-cells = <2>;
		interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "pcie_irq";
		clocks = <&scu EN7523_CLK_PCIE>;
		clock-names = "sys_ck1";
		bus-range = <0x00 0xff>;
		ranges = <0x82000000 0 0x28000000  0x28000000  0 0x8000000>;
		status = "disabled";

		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pcie_intc1 0>,
				<0 0 0 2 &pcie_intc1 1>,
				<0 0 0 3 &pcie_intc1 2>,
				<0 0 0 4 &pcie_intc1 3>;
		pcie_intc1: interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
		};
	};
};
