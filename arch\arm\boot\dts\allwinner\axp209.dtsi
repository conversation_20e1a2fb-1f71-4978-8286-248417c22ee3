/*
 * Copyright 2015 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/*
 * AXP202/209 Integrated Power Management Chip
 * http://www.x-powers.com/product/AXP20X.php
 * http://dl.linux-sunxi.org/AXP/AXP209%20Datasheet%20v1.0_cn.pdf
 */

/ {
	pmic-temp {
		compatible = "iio-hwmon";
		io-channels = <&axp_adc 4>; /* Internal temperature */
	};
};

&axp209 {
	compatible = "x-powers,axp209";
	interrupt-controller;
	#interrupt-cells = <1>;

	ac_power_supply: ac-power {
		compatible = "x-powers,axp202-ac-power-supply";
		status = "disabled";
	};

	axp_adc: adc {
		compatible = "x-powers,axp209-adc";
		#io-channel-cells = <1>;
	};

	axp_gpio: gpio {
		compatible = "x-powers,axp209-gpio";
		gpio-controller;
		#gpio-cells = <2>;
	};

	battery_power_supply: battery-power {
		compatible = "x-powers,axp209-battery-power-supply";
		status = "disabled";
	};

	regulators {
		/* Default work frequency for buck regulators */
		x-powers,dcdc-freq = <1500>;

		reg_dcdc2: dcdc2 {
			regulator-name = "dcdc2";
		};

		reg_dcdc3: dcdc3 {
			regulator-name = "dcdc3";
		};

		reg_ldo1: ldo1 {
			/* LDO1 is a fixed output regulator */
			regulator-always-on;
			regulator-min-microvolt = <1300000>;
			regulator-max-microvolt = <1300000>;
			regulator-name = "ldo1";
		};

		reg_ldo2: ldo2 {
			regulator-name = "ldo2";
		};

		reg_ldo3: ldo3 {
			regulator-name = "ldo3";
		};

		reg_ldo4: ldo4 {
			regulator-name = "ldo4";
		};

		reg_ldo5: ldo5 {
			regulator-name = "ldo5";
			status = "disabled";
		};
	};

	usb_power_supply: usb-power {
		compatible = "x-powers,axp202-usb-power-supply";
		status = "disabled";
	};
};
