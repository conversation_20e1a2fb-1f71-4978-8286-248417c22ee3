/*
 * Copyright 2015 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/*
 * AXP221/221s/223 Integrated Power Management Chip
 * http://www.x-powers.com/product/AXP22X.php
 * http://dl.linux-sunxi.org/AXP/AXP221%20Datasheet%20V1.2%2020130326%20.pdf
 */

&axp22x {
	interrupt-controller;
	#interrupt-cells = <1>;

	ac_power_supply: ac-power {
		compatible = "x-powers,axp221-ac-power-supply";
		status = "disabled";
	};

	axp_adc: adc {
		compatible = "x-powers,axp221-adc";
		#io-channel-cells = <1>;
	};

	battery_power_supply: battery-power {
		compatible = "x-powers,axp221-battery-power-supply";
		status = "disabled";
	};

	axp_gpio: gpio {
		compatible = "x-powers,axp221-gpio";
		gpio-controller;
		#gpio-cells = <2>;
	};

	regulators {
		/* Default work frequency for buck regulators */
		x-powers,dcdc-freq = <3000>;

		reg_dcdc1: dcdc1 {
			regulator-name = "dcdc1";
		};

		reg_dcdc2: dcdc2 {
			regulator-name = "dcdc2";
		};

		reg_dcdc3: dcdc3 {
			regulator-name = "dcdc3";
		};

		reg_dcdc4: dcdc4 {
			regulator-name = "dcdc4";
		};

		reg_dcdc5: dcdc5 {
			regulator-name = "dcdc5";
		};

		reg_dc1sw: dc1sw {
			regulator-name = "dc1sw";
		};

		reg_dc5ldo: dc5ldo {
			regulator-name = "dc5ldo";
		};

		reg_aldo1: aldo1 {
			regulator-name = "aldo1";
		};

		reg_aldo2: aldo2 {
			regulator-name = "aldo2";
		};

		reg_aldo3: aldo3 {
			regulator-name = "aldo3";
		};

		reg_dldo1: dldo1 {
			regulator-name = "dldo1";
		};

		reg_dldo2: dldo2 {
			regulator-name = "dldo2";
		};

		reg_dldo3: dldo3 {
			regulator-name = "dldo3";
		};

		reg_dldo4: dldo4 {
			regulator-name = "dldo4";
		};

		reg_eldo1: eldo1 {
			regulator-name = "eldo1";
		};

		reg_eldo2: eldo2 {
			regulator-name = "eldo2";
		};

		reg_eldo3: eldo3 {
			regulator-name = "eldo3";
		};

		reg_ldo_io0: ldo_io0 {
			regulator-name = "ldo_io0";
			status = "disabled";
		};

		reg_ldo_io1: ldo_io1 {
			regulator-name = "ldo_io1";
			status = "disabled";
		};

		reg_rtc_ldo: rtc_ldo {
			/* RTC_LDO is a fixed, always-on regulator */
			regulator-always-on;
			regulator-min-microvolt = <3000000>;
			regulator-max-microvolt = <3000000>;
			regulator-name = "rtc_ldo";
		};

		reg_drivevbus: drivevbus {
			regulator-name = "drivevbus";
			status = "disabled";
		};
	};

	usb_power_supply: usb-power {
		compatible = "x-powers,axp221-usb-power-supply";
		status = "disabled";
	};
};
