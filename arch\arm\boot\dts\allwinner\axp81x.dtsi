/*
 * Copyright 2017 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/* AXP813/818 Integrated Power Management Chip */

&axp81x {
	interrupt-controller;
	#interrupt-cells = <1>;

	ac_power_supply: ac-power {
		compatible = "x-powers,axp813-ac-power-supply";
		status = "disabled";
	};

	axp_adc: adc {
		compatible = "x-powers,axp813-adc";
		#io-channel-cells = <1>;
	};

	axp_gpio: gpio {
		compatible = "x-powers,axp813-gpio";
		gpio-controller;
		#gpio-cells = <2>;
	};

	battery_power_supply: battery-power {
		compatible = "x-powers,axp813-battery-power-supply";
		status = "disabled";
	};

	regulators {
		/* Default work frequency for buck regulators */
		x-powers,dcdc-freq = <3000>;

		reg_dcdc1: dcdc1 {
		};

		reg_dcdc2: dcdc2 {
		};

		reg_dcdc3: dcdc3 {
		};

		reg_dcdc4: dcdc4 {
		};

		reg_dcdc5: dcdc5 {
		};

		reg_dcdc6: dcdc6 {
		};

		reg_dcdc7: dcdc7 {
		};

		reg_aldo1: aldo1 {
		};

		reg_aldo2: aldo2 {
		};

		reg_aldo3: aldo3 {
		};

		reg_dldo1: dldo1 {
		};

		reg_dldo2: dldo2 {
		};

		reg_dldo3: dldo3 {
		};

		reg_dldo4: dldo4 {
		};

		reg_eldo1: eldo1 {
		};

		reg_eldo2: eldo2 {
		};

		reg_eldo3: eldo3 {
		};

		reg_fldo1: fldo1 {
		};

		reg_fldo2: fldo2 {
		};

		reg_fldo3: fldo3 {
		};

		reg_ldo_io0: ldo-io0 {
			/* Disable by default to avoid conflicts with GPIO */
			status = "disabled";
		};

		reg_ldo_io1: ldo-io1 {
			/* Disable by default to avoid conflicts with GPIO */
			status = "disabled";
		};

		reg_rtc_ldo: rtc-ldo {
			/* RTC_LDO is a fixed, always-on regulator */
			regulator-always-on;
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <1800000>;
		};

		reg_sw: sw {
		};

		reg_drivevbus: drivevbus {
			status = "disabled";
		};
	};

	usb_power_supply: usb-power {
		compatible = "x-powers,axp813-usb-power-supply";
	};
};
