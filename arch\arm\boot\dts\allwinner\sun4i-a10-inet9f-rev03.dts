/*
 * Copyright 2015 <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun4i-a10.dtsi"
#include "sunxi-common-regulators.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "iNet-9F Rev 03";
	compatible = "inet-tek,inet9f-rev03", "allwinner,sun4i-a10";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	gpio-keys {
		compatible = "gpio-keys-polled";
		poll-interval = <20>;

		event-left-joystick-left {
			label = "Left Joystick Left";
			linux,code = <ABS_X>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <0xffffffff>; /* -1 */
			gpios = <&pio 0 6 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA6 */
		};

		event-left-joystick-right {
			label = "Left Joystick Right";
			linux,code = <ABS_X>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <1>;
			gpios = <&pio 0 5 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA5 */
		};

		event-left-joystick-up {
			label = "Left Joystick Up";
			linux,code = <ABS_Y>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <0xffffffff>; /* -1 */
			gpios = <&pio 0 8 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA8 */
		};

		event-left-joystick-down {
			label = "Left Joystick Down";
			linux,code = <ABS_Y>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <1>;
			gpios = <&pio 0 9 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA9 */
		};

		event-right-joystick-left {
			label = "Right Joystick Left";
			linux,code = <ABS_Z>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <0xffffffff>; /* -1 */
			gpios = <&pio 0 1 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA1 */
		};

		event-right-joystick-right {
			label = "Right Joystick Right";
			linux,code = <ABS_Z>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <1>;
			gpios = <&pio 0 0 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA0 */
		};

		event-right-joystick-up {
			label = "Right Joystick Up";
			linux,code = <ABS_RZ>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <0xffffffff>; /* -1 */
			gpios = <&pio 0 3 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA3 */
		};

		event-right-joystick-down {
			label = "Right Joystick Down";
			linux,code = <ABS_RZ>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <1>;
			gpios = <&pio 0 4 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA4 */
		};

		event-dpad-left {
			label = "DPad Left";
			linux,code = <ABS_HAT0X>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <0xffffffff>; /* -1 */
			gpios = <&pio 7 23 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PH23 */
		};

		event-dpad-right {
			label = "DPad Right";
			linux,code = <ABS_HAT0X>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <1>;
			gpios = <&pio 7 24 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PH24 */
		};

		event-dpad-up {
			label = "DPad Up";
			linux,code = <ABS_HAT0Y>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <0xffffffff>; /* -1 */
			gpios = <&pio 7 25 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PH25 */
		};

		event-dpad-down {
			label = "DPad Down";
			linux,code = <ABS_HAT0Y>;
			linux,input-type = <EV_ABS>;
			linux,input-value = <1>;
			gpios = <&pio 7 26 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PH26 */
		};

		event-x {
			label = "Button X";
			linux,code = <BTN_X>;
			gpios = <&pio 0 16 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA16 */
		};

		event-y {
			label = "Button Y";
			linux,code = <BTN_Y>;
			gpios = <&pio 0 14 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA14 */
		};

		event-a {
			label = "Button A";
			linux,code = <BTN_A>;
			gpios = <&pio 0 17 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA17 */
		};

		event-b {
			label = "Button B";
			linux,code = <BTN_B>;
			gpios = <&pio 0 15 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA15 */
		};

		event-select {
			label = "Select Button";
			linux,code = <BTN_SELECT>;
			gpios = <&pio 0 11 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA11 */
		};

		event-start {
			label = "Start Button";
			linux,code = <BTN_START>;
			gpios = <&pio 0 12 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA12 */
		};

		event-top-left {
			label = "Top Left Button";
			linux,code = <BTN_TL>;
			gpios = <&pio 7 22 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PH22 */
		};

		event-top-right {
			label = "Top Right Button";
			linux,code = <BTN_TR>;
			gpios = <&pio 0 13 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PA13 */
		};
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&ehci1 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupts = <0>;
	};
};

#include "axp209.dtsi"

&i2c1 {
	status = "okay";

	/* Accelerometer */
	bma250@18 {
		compatible = "bosch,bma250";
		reg = <0x18>;
		interrupt-parent = <&pio>;
		interrupts = <7 0 IRQ_TYPE_EDGE_RISING>; /* PH0 / EINT0 */
	};
};

&i2c2 {
	status = "okay";

	ft5406ee8: touchscreen@38 {
		compatible = "edt,edt-ft5406";
		reg = <0x38>;
		interrupt-parent = <&pio>;
		interrupts = <7 21 IRQ_TYPE_EDGE_FALLING>;
		touchscreen-size-x = <800>;
		touchscreen-size-y = <480>;
	};
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-200 {
		label = "Menu";
		linux,code = <KEY_MENU>;
		channel = <0>;
		voltage = <200000>;
	};

	button-600 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <600000>;
	};

	button-800 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <800000>;
	};

	button-1000 {
		label = "Home";
		linux,code = <KEY_HOMEPAGE>;
		channel = <0>;
		voltage = <1000000>;
	};

	button-1200 {
		label = "Esc";
		linux,code = <KEY_ESC>;
		channel = <0>;
		voltage = <1200000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1250000>;
	regulator-max-microvolt = <1250000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_usb0_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 4 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PH4 */
	usb0_vbus_det-gpios = <&pio 7 5 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>; /* PH5 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
