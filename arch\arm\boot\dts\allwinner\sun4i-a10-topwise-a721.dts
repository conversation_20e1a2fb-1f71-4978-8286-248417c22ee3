// SPDX-License-Identifier: GPL-2.0+
/*
 * Copyright 2020 <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun4i-a10.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "Topwise A721";
	compatible = "topwise,a721", "allwinner,sun4i-a10";

	aliases {
		serial0 = &uart0;
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm 0 100000 PWM_POLARITY_INVERTED>;
		power-supply = <&reg_vbat>;
		enable-gpios = <&pio 7 7 GPIO_ACTIVE_HIGH>; /* PH7 */
		brightness-levels = <0 30 40 50 60 70 80 90 100>;
		default-brightness-level = <8>;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	panel {
		compatible = "starry,kr070pe2t";
		backlight = <&backlight>;
		power-supply = <&reg_lcd_power>;

		port {
			panel_input: endpoint {
				remote-endpoint = <&tcon0_out_panel>;
			};
		};
	};

	reg_lcd_power: reg-lcd-power {
		compatible = "regulator-fixed";
		regulator-name = "reg-lcd-power";
		gpio = <&pio 7 8 GPIO_ACTIVE_HIGH>; /* PH8 */
		enable-active-high;
	};

	reg_vbat: reg-vbat {
		compatible = "regulator-fixed";
		regulator-name = "vbat";
		regulator-min-microvolt = <3700000>;
		regulator-max-microvolt = <3700000>;
	};

};

&codec {
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupts = <0>;
	};
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&i2c1 {
	status = "okay";

	accelerometer@4c {
		compatible = "fsl,mma7660";
		reg = <0x4c>;
	};
};

&i2c2 {
	status = "okay";

	touchscreen@38 {
		compatible = "edt,edt-ft5406";
		reg = <0x38>;
		interrupt-parent = <&pio>;
		interrupts = <7 21 IRQ_TYPE_EDGE_FALLING>;
		touchscreen-size-x = <800>;
		touchscreen-size-y = <480>;
		vcc-supply = <&reg_vcc3v3>;
	};
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-571 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <571428>;
	};

	button-761 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <761904>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH01 */
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pio {
	vcc-pb-supply = <&reg_vcc3v3>;
	vcc-pf-supply = <&reg_vcc3v3>;
	vcc-ph-supply = <&reg_vcc3v3>;
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pin>;
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1250000>;
	regulator-max-microvolt = <1250000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_usb0_vbus {
	status = "okay";
};

&reg_usb1_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&tcon0_out {
	tcon0_out_panel: endpoint@0 {
		reg = <0>;
		remote-endpoint = <&panel_input>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 4 GPIO_ACTIVE_HIGH>; /* PH4 */
	usb0_vbus_det-gpios = <&pio 7 5 GPIO_ACTIVE_HIGH>; /* PH5 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
