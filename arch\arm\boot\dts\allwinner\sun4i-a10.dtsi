/*
 * Copyright 2012 <PERSON>
 * <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This library is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This library is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/thermal/thermal.h>
#include <dt-bindings/dma/sun4i-a10.h>
#include <dt-bindings/clock/sun4i-a10-ccu.h>
#include <dt-bindings/reset/sun4i-a10-ccu.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&intc>;

	aliases {
		ethernet0 = &emac;
	};

	chosen {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		framebuffer-lcd0-hdmi {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_be0-lcd0-hdmi";
			clocks = <&ccu CLK_AHB_LCD0>, <&ccu CLK_AHB_HDMI0>,
				 <&ccu CLK_AHB_DE_BE0>, <&ccu CLK_DE_BE0>,
				 <&ccu CLK_TCON0_CH1>, <&ccu CLK_DRAM_DE_BE0>;
			status = "disabled";
		};

		framebuffer-fe0-lcd0-hdmi {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_fe0-de_be0-lcd0-hdmi";
			clocks = <&ccu CLK_AHB_LCD0>, <&ccu CLK_AHB_HDMI0>,
				 <&ccu CLK_AHB_DE_BE0>, <&ccu CLK_AHB_DE_FE0>,
				 <&ccu CLK_DE_BE0>, <&ccu CLK_DE_FE0>,
				 <&ccu CLK_TCON0_CH1>, <&ccu CLK_HDMI>,
				 <&ccu CLK_DRAM_DE_FE0>, <&ccu CLK_DRAM_DE_BE0>;
			status = "disabled";
		};

		framebuffer-fe0-lcd0 {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_fe0-de_be0-lcd0";
			clocks = <&ccu CLK_AHB_LCD0>, <&ccu CLK_AHB_DE_BE0>,
				 <&ccu CLK_AHB_DE_FE0>, <&ccu CLK_DE_BE0>,
				 <&ccu CLK_DE_FE0>, <&ccu CLK_TCON0_CH0>,
				 <&ccu CLK_DRAM_DE_FE0>, <&ccu CLK_DRAM_DE_BE0>;
			status = "disabled";
		};

		framebuffer-fe0-lcd0-tve0 {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_fe0-de_be0-lcd0-tve0";
			clocks = <&ccu CLK_AHB_TVE0>, <&ccu CLK_AHB_LCD0>,
				 <&ccu CLK_AHB_DE_BE0>, <&ccu CLK_AHB_DE_FE0>,
				 <&ccu CLK_DE_BE0>, <&ccu CLK_DE_FE0>,
				 <&ccu CLK_TCON0_CH1>, <&ccu CLK_DRAM_TVE0>,
				 <&ccu CLK_DRAM_DE_FE0>, <&ccu CLK_DRAM_DE_BE0>;
			status = "disabled";
		};
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a8";
			reg = <0x0>;
			clocks = <&ccu CLK_CPU>;
			clock-latency = <244144>; /* 8 32k periods */
			operating-points =
				/* kHz	  uV */
				<1008000 1400000>,
				<912000	1350000>,
				<864000	1300000>,
				<624000	1250000>;
			#cooling-cells = <2>;
		};
	};

	thermal-zones {
		cpu-thermal {
			/* milliseconds */
			polling-delay-passive = <250>;
			polling-delay = <1000>;
			thermal-sensors = <&rtp>;

			cooling-maps {
				map0 {
					trip = <&cpu_alert0>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};

			trips {
				cpu_alert0: cpu-alert0 {
					/* milliCelsius */
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_crit: cpu-crit {
					/* milliCelsius */
					temperature = <100000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: clk-24M {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-output-names = "osc24M";
		};

		osc32k: clk-32k {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-output-names = "osc32k";
		};
	};

	de: display-engine {
		compatible = "allwinner,sun4i-a10-display-engine";
		allwinner,pipelines = <&fe0>, <&fe1>;
		status = "disabled";
	};

	pmu {
		compatible = "arm,cortex-a8-pmu";
		interrupts = <3>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		/* Address must be kept in the lower 256 MiBs of DRAM for VE. */
		default-pool {
			compatible = "shared-dma-pool";
			size = <0x6000000>;
			alloc-ranges = <0x40000000 0x10000000>;
			reusable;
			linux,cma-default;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		system-control@1c00000 {
			compatible = "allwinner,sun4i-a10-system-control";
			reg = <0x01c00000 0x30>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			sram_a: sram@0 {
				compatible = "mmio-sram";
				reg = <0x00000000 0xc000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x00000000 0xc000>;

				emac_sram: sram-section@8000 {
					compatible = "allwinner,sun4i-a10-sram-a3-a4";
					reg = <0x8000 0x4000>;
					status = "disabled";
				};
			};

			sram_d: sram@10000 {
				compatible = "mmio-sram";
				reg = <0x00010000 0x1000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x00010000 0x1000>;

				otg_sram: sram-section@0 {
					compatible = "allwinner,sun4i-a10-sram-d";
					reg = <0x0000 0x1000>;
					status = "disabled";
				};
			};

			sram_c: sram@1d00000 {
				compatible = "mmio-sram";
				reg = <0x01d00000 0xd0000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x01d00000 0xd0000>;

				ve_sram: sram-section@0 {
					compatible = "allwinner,sun4i-a10-sram-c1";
					reg = <0x000000 0x80000>;
				};
			};
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun4i-a10-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <27>;
			clocks = <&ccu CLK_AHB_DMA>;
			#dma-cells = <2>;
		};

		nfc: nand-controller@1c03000 {
			compatible = "allwinner,sun4i-a10-nand";
			reg = <0x01c03000 0x1000>;
			interrupts = <37>;
			clocks = <&ccu CLK_AHB_NAND>, <&ccu CLK_NAND>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 3>;
			dma-names = "rxtx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi0: spi@1c05000 {
			compatible = "allwinner,sun4i-a10-spi";
			reg = <0x01c05000 0x1000>;
			interrupts = <10>;
			clocks = <&ccu CLK_AHB_SPI0>, <&ccu CLK_SPI0>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 27>,
			       <&dma SUN4I_DMA_DEDICATED 26>;
			dma-names = "rx", "tx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi1: spi@1c06000 {
			compatible = "allwinner,sun4i-a10-spi";
			reg = <0x01c06000 0x1000>;
			interrupts = <11>;
			clocks = <&ccu CLK_AHB_SPI1>, <&ccu CLK_SPI1>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 9>,
			       <&dma SUN4I_DMA_DEDICATED 8>;
			dma-names = "rx", "tx";
			pinctrl-names = "default";
			pinctrl-0 = <&spi1_pins>, <&spi1_cs0_pin>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		emac: ethernet@1c0b000 {
			compatible = "allwinner,sun4i-a10-emac";
			reg = <0x01c0b000 0x1000>;
			interrupts = <55>;
			clocks = <&ccu CLK_AHB_EMAC>;
			allwinner,sram = <&emac_sram 1>;
			pinctrl-names = "default";
			pinctrl-0 = <&emac_pins>;
			status = "disabled";
		};

		mdio: mdio@1c0b080 {
			compatible = "allwinner,sun4i-a10-mdio";
			reg = <0x01c0b080 0x14>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		tcon0: lcd-controller@1c0c000 {
			compatible = "allwinner,sun4i-a10-tcon";
			reg = <0x01c0c000 0x1000>;
			interrupts = <44>;
			resets = <&ccu RST_TCON0>;
			reset-names = "lcd";
			clocks = <&ccu CLK_AHB_LCD0>,
				 <&ccu CLK_TCON0_CH0>,
				 <&ccu CLK_TCON0_CH1>;
			clock-names = "ahb",
				      "tcon-ch0",
				      "tcon-ch1";
			clock-output-names = "tcon0-pixel-clock";
			#clock-cells = <0>;
			dmas = <&dma SUN4I_DMA_DEDICATED 14>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon0_in_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_out_tcon0>;
					};

					tcon0_in_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon0_out_hdmi: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&hdmi_in_tcon0>;
						allwinner,tcon-channel = <1>;
					};
				};
			};
		};

		tcon1: lcd-controller@1c0d000 {
			compatible = "allwinner,sun4i-a10-tcon";
			reg = <0x01c0d000 0x1000>;
			interrupts = <45>;
			resets = <&ccu RST_TCON1>;
			reset-names = "lcd";
			clocks = <&ccu CLK_AHB_LCD1>,
				 <&ccu CLK_TCON1_CH0>,
				 <&ccu CLK_TCON1_CH1>;
			clock-names = "ahb",
				      "tcon-ch0",
				      "tcon-ch1";
			clock-output-names = "tcon1-pixel-clock";
			#clock-cells = <0>;
			dmas = <&dma SUN4I_DMA_DEDICATED 15>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon1_in_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_out_tcon1>;
					};

					tcon1_in_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_out_tcon1>;
					};
				};

				tcon1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon1_out_hdmi: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&hdmi_in_tcon1>;
						allwinner,tcon-channel = <1>;
					};
				};
			};
		};

		video-codec@1c0e000 {
			compatible = "allwinner,sun4i-a10-video-engine";
			reg = <0x01c0e000 0x1000>;
			clocks = <&ccu CLK_AHB_VE>, <&ccu CLK_VE>,
				 <&ccu CLK_DRAM_VE>;
			clock-names = "ahb", "mod", "ram";
			resets = <&ccu RST_VE>;
			interrupts = <53>;
			allwinner,sram = <&ve_sram 1>;
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun4i-a10-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_AHB_MMC0>, <&ccu CLK_MMC0>;
			clock-names = "ahb", "mmc";
			interrupts = <32>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun4i-a10-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_AHB_MMC1>, <&ccu CLK_MMC1>;
			clock-names = "ahb", "mmc";
			interrupts = <33>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun4i-a10-mmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&ccu CLK_AHB_MMC2>, <&ccu CLK_MMC2>;
			clock-names = "ahb", "mmc";
			interrupts = <34>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc3: mmc@1c12000 {
			compatible = "allwinner,sun4i-a10-mmc";
			reg = <0x01c12000 0x1000>;
			clocks = <&ccu CLK_AHB_MMC3>, <&ccu CLK_MMC3>;
			clock-names = "ahb", "mmc";
			interrupts = <35>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		usb_otg: usb@1c13000 {
			compatible = "allwinner,sun4i-a10-musb";
			reg = <0x01c13000 0x0400>;
			clocks = <&ccu CLK_AHB_OTG>;
			interrupts = <38>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			allwinner,sram = <&otg_sram 1>;
			dr_mode = "otg";
			status = "disabled";
		};

		usbphy: phy@1c13400 {
			#phy-cells = <1>;
			compatible = "allwinner,sun4i-a10-usb-phy";
			reg = <0x01c13400 0x10>, <0x01c14800 0x4>, <0x01c1c800 0x4>;
			reg-names = "phy_ctrl", "pmu1", "pmu2";
			clocks = <&ccu CLK_USB_PHY>;
			clock-names = "usb_phy";
			resets = <&ccu RST_USB_PHY0>,
				 <&ccu RST_USB_PHY1>,
				 <&ccu RST_USB_PHY2>;
			reset-names = "usb0_reset", "usb1_reset", "usb2_reset";
			status = "disabled";
		};

		ehci0: usb@1c14000 {
			compatible = "allwinner,sun4i-a10-ehci", "generic-ehci";
			reg = <0x01c14000 0x100>;
			interrupts = <39>;
			clocks = <&ccu CLK_AHB_EHCI0>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci0: usb@1c14400 {
			compatible = "allwinner,sun4i-a10-ohci", "generic-ohci";
			reg = <0x01c14400 0x100>;
			interrupts = <64>;
			clocks = <&ccu CLK_USB_OHCI0>, <&ccu CLK_AHB_OHCI0>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		crypto: crypto-engine@1c15000 {
			compatible = "allwinner,sun4i-a10-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <86>;
			clocks = <&ccu CLK_AHB_SS>, <&ccu CLK_SS>;
			clock-names = "ahb", "mod";
		};

		hdmi: hdmi@1c16000 {
			compatible = "allwinner,sun4i-a10-hdmi";
			reg = <0x01c16000 0x1000>;
			interrupts = <58>;
			clocks = <&ccu CLK_AHB_HDMI0>, <&ccu CLK_HDMI>,
				 <&ccu CLK_PLL_VIDEO0_2X>,
				 <&ccu CLK_PLL_VIDEO1_2X>;
			clock-names = "ahb", "mod", "pll-0", "pll-1";
			dmas = <&dma SUN4I_DMA_NORMAL 16>,
			       <&dma SUN4I_DMA_NORMAL 16>,
			       <&dma SUN4I_DMA_DEDICATED 24>;
			dma-names = "ddc-tx", "ddc-rx", "audio-tx";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					hdmi_in_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_out_hdmi>;
					};

					hdmi_in_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_out_hdmi>;
					};
				};

				hdmi_out: port@1 {
					reg = <1>;
				};
			};
		};

		spi2: spi@1c17000 {
			compatible = "allwinner,sun4i-a10-spi";
			reg = <0x01c17000 0x1000>;
			interrupts = <12>;
			clocks = <&ccu CLK_AHB_SPI2>, <&ccu CLK_SPI2>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 29>,
			       <&dma SUN4I_DMA_DEDICATED 28>;
			dma-names = "rx", "tx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		ahci: sata@1c18000 {
			compatible = "allwinner,sun4i-a10-ahci";
			reg = <0x01c18000 0x1000>;
			interrupts = <56>;
			clocks = <&ccu CLK_AHB_SATA>, <&ccu CLK_SATA>;
			status = "disabled";
		};

		ehci1: usb@1c1c000 {
			compatible = "allwinner,sun4i-a10-ehci", "generic-ehci";
			reg = <0x01c1c000 0x100>;
			interrupts = <40>;
			clocks = <&ccu CLK_AHB_EHCI1>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci1: usb@1c1c400 {
			compatible = "allwinner,sun4i-a10-ohci", "generic-ohci";
			reg = <0x01c1c400 0x100>;
			interrupts = <65>;
			clocks = <&ccu CLK_USB_OHCI1>, <&ccu CLK_AHB_OHCI1>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		csi1: csi@1c1d000 {
			compatible = "allwinner,sun4i-a10-csi1";
			reg = <0x01c1d000 0x1000>;
			interrupts = <43>;
			clocks = <&ccu CLK_AHB_CSI1>, <&ccu CLK_DRAM_CSI1>;
			clock-names = "bus", "ram";
			resets = <&ccu RST_CSI1>;
			status = "disabled";
		};

		spi3: spi@1c1f000 {
			compatible = "allwinner,sun4i-a10-spi";
			reg = <0x01c1f000 0x1000>;
			interrupts = <50>;
			clocks = <&ccu CLK_AHB_SPI3>, <&ccu CLK_SPI3>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 31>,
			       <&dma SUN4I_DMA_DEDICATED 30>;
			dma-names = "rx", "tx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		ccu: clock@1c20000 {
			compatible = "allwinner,sun4i-a10-ccu";
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&osc32k>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		intc: interrupt-controller@1c20400 {
			compatible = "allwinner,sun4i-a10-ic";
			reg = <0x01c20400 0x400>;
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			compatible = "allwinner,sun4i-a10-pinctrl";
			reg = <0x01c20800 0x400>;
			interrupts = <28>;
			clocks = <&ccu CLK_APB0_PIO>, <&osc24M>, <&osc32k>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			can0_ph_pins: can0-ph-pins {
				pins = "PH20", "PH21";
				function = "can";
			};

			/omit-if-no-ref/
			csi1_8bits_pg_pins: csi1-8bits-pg-pins {
				pins = "PG0", "PG2", "PG3", "PG4", "PG5",
				       "PG6", "PG7", "PG8", "PG9", "PG10",
				       "PG11";
				function = "csi1";
			};

			/omit-if-no-ref/
			csi1_24bits_ph_pins: csi1-24bits-ph-pins {
				pins = "PH0", "PH1", "PH2", "PH3", "PH4",
				       "PH5", "PH6", "PH7", "PH8", "PH9",
				       "PH10", "PH11", "PH12", "PH13", "PH14",
				       "PH15", "PH16", "PH17", "PH18", "PH19",
				       "PH20", "PH21", "PH22", "PH23", "PH24",
				       "PH25", "PH26", "PH27";
				function = "csi1";
			};

			/omit-if-no-ref/
			csi1_clk_pg_pin: csi1-clk-pg-pin {
				pins = "PG1";
				function = "csi1";
			};

			emac_pins: emac0-pins {
				pins = "PA0", "PA1", "PA2",
				       "PA3", "PA4", "PA5", "PA6",
				       "PA7", "PA8", "PA9", "PA10",
				       "PA11", "PA12", "PA13", "PA14",
				       "PA15", "PA16";
				function = "emac";
			};

			i2c0_pins: i2c0-pins {
				pins = "PB0", "PB1";
				function = "i2c0";
			};

			i2c1_pins: i2c1-pins {
				pins = "PB18", "PB19";
				function = "i2c1";
			};

			i2c2_pins: i2c2-pins {
				pins = "PB20", "PB21";
				function = "i2c2";
			};

			ir0_rx_pins: ir0-rx-pin {
				pins = "PB4";
				function = "ir0";
			};

			ir0_tx_pins: ir0-tx-pin {
				pins = "PB3";
				function = "ir0";
			};

			ir1_rx_pins: ir1-rx-pin {
				pins = "PB23";
				function = "ir1";
			};

			ir1_tx_pins: ir1-tx-pin {
				pins = "PB22";
				function = "ir1";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2",
				       "PF3", "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			ps2_ch0_pins: ps2-ch0-pins {
				pins = "PI20", "PI21";
				function = "ps2";
			};

			ps2_ch1_ph_pins: ps2-ch1-ph-pins {
				pins = "PH12", "PH13";
				function = "ps2";
			};

			pwm0_pin: pwm0-pin {
				pins = "PB2";
				function = "pwm";
			};

			pwm1_pin: pwm1-pin {
				pins = "PI3";
				function = "pwm";
			};

			spdif_tx_pin: spdif-tx-pin {
				pins = "PB13";
				function = "spdif";
				bias-pull-up;
			};

			spi0_pi_pins: spi0-pi-pins {
				pins = "PI11", "PI12", "PI13";
				function = "spi0";
			};

			spi0_cs0_pi_pin: spi0-cs0-pi-pin {
				pins = "PI10";
				function = "spi0";
			};

			spi1_pins: spi1-pins {
				pins = "PI17", "PI18", "PI19";
				function = "spi1";
			};

			spi1_cs0_pin: spi1-cs0-pin {
				pins = "PI16";
				function = "spi1";
			};

			spi2_pb_pins: spi2-pb-pins {
				pins = "PB15", "PB16", "PB17";
				function = "spi2";
			};

			spi2_pc_pins: spi2-pc-pins {
				pins = "PC20", "PC21", "PC22";
				function = "spi2";
			};

			spi2_cs0_pb_pin: spi2-cs0-pb-pin {
				pins = "PB14";
				function = "spi2";
			};

			spi2_cs0_pc_pins: spi2-cs0-pc-pin {
				pins = "PC19";
				function = "spi2";
			};

			uart0_pb_pins: uart0-pb-pins {
				pins = "PB22", "PB23";
				function = "uart0";
			};

			uart0_pf_pins: uart0-pf-pins {
				pins = "PF2", "PF4";
				function = "uart0";
			};

			uart1_pins: uart1-pins {
				pins = "PA10", "PA11";
				function = "uart1";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun4i-a10-timer";
			reg = <0x01c20c00 0x90>;
			interrupts = <22>,
				     <23>,
				     <24>,
				     <25>,
				     <67>,
				     <68>;
			clocks = <&osc24M>;
		};

		wdt: watchdog@1c20c90 {
			compatible = "allwinner,sun4i-a10-wdt";
			reg = <0x01c20c90 0x10>;
			interrupts = <24>;
			clocks = <&osc24M>;
		};

		rtc: rtc@1c20d00 {
			compatible = "allwinner,sun4i-a10-rtc";
			reg = <0x01c20d00 0x20>;
			interrupts = <24>;
		};

		pwm: pwm@1c20e00 {
			compatible = "allwinner,sun4i-a10-pwm";
			reg = <0x01c20e00 0xc>;
			clocks = <&osc24M>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		spdif: spdif@1c21000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun4i-a10-spdif";
			reg = <0x01c21000 0x400>;
			interrupts = <13>;
			clocks = <&ccu CLK_APB0_SPDIF>, <&ccu CLK_SPDIF>;
			clock-names = "apb", "spdif";
			dmas = <&dma SUN4I_DMA_NORMAL 2>,
			       <&dma SUN4I_DMA_NORMAL 2>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		ir0: ir@1c21800 {
			compatible = "allwinner,sun4i-a10-ir";
			clocks = <&ccu CLK_APB0_IR0>, <&ccu CLK_IR0>;
			clock-names = "apb", "ir";
			interrupts = <5>;
			reg = <0x01c21800 0x40>;
			status = "disabled";
		};

		ir1: ir@1c21c00 {
			compatible = "allwinner,sun4i-a10-ir";
			clocks = <&ccu CLK_APB0_IR1>, <&ccu CLK_IR1>;
			clock-names = "apb", "ir";
			interrupts = <6>;
			reg = <0x01c21c00 0x40>;
			status = "disabled";
		};

		i2s0: i2s@1c22400 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun4i-a10-i2s";
			reg = <0x01c22400 0x400>;
			interrupts = <16>;
			clocks = <&ccu CLK_APB0_I2S0>, <&ccu CLK_I2S0>;
			clock-names = "apb", "mod";
			dmas = <&dma SUN4I_DMA_NORMAL 3>,
			       <&dma SUN4I_DMA_NORMAL 3>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		lradc: lradc@1c22800 {
			compatible = "allwinner,sun4i-a10-lradc-keys";
			reg = <0x01c22800 0x100>;
			interrupts = <31>;
			status = "disabled";
		};

		codec: codec@1c22c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun4i-a10-codec";
			reg = <0x01c22c00 0x40>;
			interrupts = <30>;
			clocks = <&ccu CLK_APB0_CODEC>, <&ccu CLK_CODEC>;
			clock-names = "apb", "codec";
			dmas = <&dma SUN4I_DMA_NORMAL 19>,
			       <&dma SUN4I_DMA_NORMAL 19>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		sid: eeprom@1c23800 {
			compatible = "allwinner,sun4i-a10-sid";
			reg = <0x01c23800 0x10>;
		};

		rtp: rtp@1c25000 {
			compatible = "allwinner,sun4i-a10-ts";
			reg = <0x01c25000 0x100>;
			interrupts = <29>;
			#thermal-sensor-cells = <0>;
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <1>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART0>;
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <2>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART1>;
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <3>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART2>;
			status = "disabled";
		};

		uart3: serial@1c28c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28c00 0x400>;
			interrupts = <4>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART3>;
			status = "disabled";
		};

		uart4: serial@1c29000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29000 0x400>;
			interrupts = <17>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART4>;
			status = "disabled";
		};

		uart5: serial@1c29400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29400 0x400>;
			interrupts = <18>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART5>;
			status = "disabled";
		};

		uart6: serial@1c29800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29800 0x400>;
			interrupts = <19>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART6>;
			status = "disabled";
		};

		uart7: serial@1c29c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29c00 0x400>;
			interrupts = <20>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART7>;
			status = "disabled";
		};

		ps20: ps2@1c2a000 {
			compatible = "allwinner,sun4i-a10-ps2";
			reg = <0x01c2a000 0x400>;
			interrupts = <62>;
			clocks = <&ccu CLK_APB1_PS20>;
			status = "disabled";
		};

		ps21: ps2@1c2a400 {
			compatible = "allwinner,sun4i-a10-ps2";
			reg = <0x01c2a400 0x400>;
			interrupts = <63>;
			clocks = <&ccu CLK_APB1_PS21>;
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun4i-a10-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <7>;
			clocks = <&ccu CLK_APB1_I2C0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun4i-a10-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <8>;
			clocks = <&ccu CLK_APB1_I2C1>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@1c2b400 {
			compatible = "allwinner,sun4i-a10-i2c";
			reg = <0x01c2b400 0x400>;
			interrupts = <9>;
			clocks = <&ccu CLK_APB1_I2C2>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		can0: can@1c2bc00 {
			compatible = "allwinner,sun4i-a10-can";
			reg = <0x01c2bc00 0x400>;
			interrupts = <26>;
			clocks = <&ccu CLK_APB1_CAN>;
			status = "disabled";
		};

		mali: gpu@1c40000 {
			compatible = "allwinner,sun4i-a10-mali", "arm,mali-400";
			reg = <0x01c40000 0x10000>;
			interrupts = <69>,
				     <70>,
				     <71>,
				     <72>,
				     <73>;
			interrupt-names = "gp",
					  "gpmmu",
					  "pp0",
					  "ppmmu0",
					  "pmu";
			clocks = <&ccu CLK_AHB_GPU>, <&ccu CLK_GPU>;
			clock-names = "bus", "core";
			resets = <&ccu RST_GPU>;

			assigned-clocks = <&ccu CLK_GPU>;
			assigned-clock-rates = <384000000>;
		};

		fe0: display-frontend@1e00000 {
			compatible = "allwinner,sun4i-a10-display-frontend";
			reg = <0x01e00000 0x20000>;
			interrupts = <47>;
			clocks = <&ccu CLK_AHB_DE_FE0>, <&ccu CLK_DE_FE0>,
				 <&ccu CLK_DRAM_DE_FE0>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_DE_FE0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					fe0_out_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_in_fe0>;
					};

					fe0_out_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_in_fe0>;
					};
				};
			};
		};

		fe1: display-frontend@1e20000 {
			compatible = "allwinner,sun4i-a10-display-frontend";
			reg = <0x01e20000 0x20000>;
			interrupts = <48>;
			clocks = <&ccu CLK_AHB_DE_FE1>, <&ccu CLK_DE_FE1>,
				 <&ccu CLK_DRAM_DE_FE1>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_DE_FE1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					fe1_out_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_in_fe1>;
					};

					fe1_out_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_in_fe1>;
					};
				};
			};
		};

		be1: display-backend@1e40000 {
			compatible = "allwinner,sun4i-a10-display-backend";
			reg = <0x01e40000 0x10000>;
			interrupts = <48>;
			clocks = <&ccu CLK_AHB_DE_BE1>, <&ccu CLK_DE_BE1>,
				 <&ccu CLK_DRAM_DE_BE1>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_DE_BE1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					be1_in_fe0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&fe0_out_be1>;
					};

					be1_in_fe1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&fe1_out_be1>;
					};
				};

				be1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					be1_out_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_in_be1>;
					};

					be1_out_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_in_be1>;
					};
				};
			};
		};

		be0: display-backend@1e60000 {
			compatible = "allwinner,sun4i-a10-display-backend";
			reg = <0x01e60000 0x10000>;
			interrupts = <47>;
			clocks = <&ccu CLK_AHB_DE_BE0>, <&ccu CLK_DE_BE0>,
				 <&ccu CLK_DRAM_DE_BE0>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_DE_BE0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be0_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					be0_in_fe0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&fe0_out_be0>;
					};

					be0_in_fe1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&fe1_out_be0>;
					};
				};

				be0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					be0_out_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_in_be0>;
					};

					be0_out_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_in_be0>;
					};
				};
			};
		};
	};
};
