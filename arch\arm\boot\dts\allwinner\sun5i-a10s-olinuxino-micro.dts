/*
 * Copyright 2013 <PERSON><PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun5i-a10s.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "Olimex A10s-Olinuxino Micro";
	compatible = "olimex,a10s-olinuxino-micro", "allwinner,sun5i-a10s";

	aliases {
		serial0 = &uart0;
		serial1 = &uart2;
		serial2 = &uart3;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&led_pins_olinuxino>;

		led {
			label = "a10s-olinuxino-micro:green:usr";
			gpios = <&pio 4 3 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};
};

&be0 {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&emac {
	pinctrl-names = "default";
	pinctrl-0 = <&emac_pa_pins>;
	phy-handle = <&phy1>;
	status = "okay";
};

&emac_sram {
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c0 {
	status = "okay";

	axp152: pmic@30 {
		reg = <0x30>;
		interrupts = <0>;
	};
};

#include "axp152.dtsi"

&i2c1 {
	status = "okay";

	eeprom@50 {
		compatible = "atmel,24c16";
		pagesize = <16>;
		reg = <0x50>;
		read-only;
	};
};

&i2c2 {
	status = "okay";
};

&lradc {
	vref-supply = <&reg_vcc3v0>;
	status = "okay";

	button-191 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <191274>;
	};

	button-392 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <392644>;
	};

	button-601 {
		label = "Menu";
		linux,code = <KEY_MENU>;
		channel = <0>;
		voltage = <601151>;
	};

	button-795 {
		label = "Enter";
		linux,code = <KEY_ENTER>;
		channel = <0>;
		voltage = <795090>;
	};

	button-987 {
		label = "Home";
		linux,code = <KEY_HOMEPAGE>;
		channel = <0>;
		voltage = <987387>;
	};
};

&mdio {
	status = "okay";

	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 6 1 GPIO_ACTIVE_LOW>; /* PG1 */
	status = "okay";
};

&mmc1 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 6 13 GPIO_ACTIVE_LOW>; /* PG13 */
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pio {
	led_pins_olinuxino: led-pin {
		pins = "PE3";
		function = "gpio_out";
		drive-strength = <20>;
	};
};

&reg_usb0_vbus {
	gpio = <&pio 6 11 GPIO_ACTIVE_HIGH>; /* PG11 */
	status = "okay";
};

&reg_usb1_vbus {
	gpio = <&pio 1 10 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pb_pins>,
		    <&spi2_cs0_pb_pin>;
	status = "okay";
};

&tcon0 {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pc_pins>;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pg_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 12 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PG12 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};
