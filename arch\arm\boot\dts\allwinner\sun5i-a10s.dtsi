/*
 * Copyright 2013 <PERSON><PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This library is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This library is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "sun5i.dtsi"

#include <dt-bindings/dma/sun4i-a10.h>

/ {
	aliases {
		ethernet0 = &emac;
	};

	chosen {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		framebuffer-lcd0-hdmi {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_be0-lcd0-hdmi";
			clocks = <&ccu CLK_AHB_LCD>, <&ccu CLK_AHB_HDMI>,
				 <&ccu CLK_AHB_DE_BE>, <&ccu CLK_DRAM_DE_BE>,
				 <&ccu CLK_DE_BE>, <&ccu CLK_HDMI>;
			status = "disabled";
		};
	};

	display-engine {
		compatible = "allwinner,sun5i-a10s-display-engine";
		allwinner,pipelines = <&fe0>;
	};

	soc {
		hdmi: hdmi@1c16000 {
			compatible = "allwinner,sun5i-a10s-hdmi";
			reg = <0x01c16000 0x1000>;
			interrupts = <58>;
			clocks = <&ccu CLK_AHB_HDMI>, <&ccu CLK_HDMI>,
				 <&ccu CLK_PLL_VIDEO0_2X>,
				 <&ccu CLK_PLL_VIDEO1_2X>;
			clock-names = "ahb", "mod", "pll-0", "pll-1";
			dmas = <&dma SUN4I_DMA_NORMAL 16>,
			       <&dma SUN4I_DMA_NORMAL 16>,
			       <&dma SUN4I_DMA_DEDICATED 24>;
			dma-names = "ddc-tx", "ddc-rx", "audio-tx";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi_in: port@0 {
					reg = <0>;

					hdmi_in_tcon0: endpoint {
						remote-endpoint = <&tcon0_out_hdmi>;
					};
				};

				hdmi_out: port@1 {
					reg = <1>;
				};
			};
		};

		pwm: pwm@1c20e00 {
			compatible = "allwinner,sun5i-a10s-pwm";
			reg = <0x01c20e00 0xc>;
			clocks = <&ccu CLK_HOSC>;
			#pwm-cells = <3>;
			status = "disabled";
		};
	};
};

&ccu {
	compatible = "allwinner,sun5i-a10s-ccu";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
};

&pio {
	compatible = "allwinner,sun5i-a10s-pinctrl";

	uart0_pb_pins: uart0-pb-pins {
		pins = "PB19", "PB20";
		function = "uart0";
	};

	uart2_pc_pins: uart2-pc-pins {
		pins = "PC18", "PC19";
		function = "uart2";
	};

	emac_pa_pins: emac-pa-pins {
		pins = "PA0", "PA1", "PA2",
				"PA3", "PA4", "PA5", "PA6",
				"PA7", "PA8", "PA9", "PA10",
				"PA11", "PA12", "PA13", "PA14",
				"PA15", "PA16";
		function = "emac";
	};

	mmc1_pins: mmc1-pins {
		pins = "PG3", "PG4", "PG5",
				 "PG6", "PG7", "PG8";
		function = "mmc1";
		drive-strength = <30>;
	};

	spi2_pb_pins: spi2-pb-pins {
		pins = "PB12", "PB13", "PB14";
		function = "spi2";
	};

	spi2_cs0_pb_pin: spi2-cs0-pb-pin {
		pins = "PB11";
		function = "spi2";
	};
};

&tcon0_out {
	tcon0_out_hdmi: endpoint@2 {
		reg = <2>;
		remote-endpoint = <&hdmi_in_tcon0>;
		allwinner,tcon-channel = <1>;
	};
};
