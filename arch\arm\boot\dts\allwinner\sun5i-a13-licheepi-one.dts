/*
 * Copyright 2016 <PERSON><PERSON>wy <PERSON> <<EMAIL>>
 *
 * Based on sun5i-a13-olinuxino.dts, which is
 *   Copyright 2012 Maxime Ripard <<EMAIL>>
 *   Copyright 2013 <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun5i-a13.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "Lichee Pi One";
	compatible = "licheepi,licheepi-one", "allwinner,sun5i-a13";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "licheepi:red:usr";
			gpios = <&pio 2 5 GPIO_ACTIVE_LOW>;
		};

		led-1 {
			label = "licheepi:green:usr";
			gpios = <&pio 2 19 GPIO_ACTIVE_LOW>;
			default-state = "on";
		};

		led-2 {
			label = "licheepi:blue:usr";
			gpios = <&pio 2 4 GPIO_ACTIVE_LOW>;
		};

	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		compatible = "x-powers,axp209";
		reg = <0x34>;
		interrupts = <0>;

		interrupt-controller;
		#interrupt-cells = <1>;
	};
};

&i2c1 {
	status = "disabled";
};

&i2c2 {
	status = "disabled";
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-984 {
		label = "Home";
		linux,code = <KEY_HOMEPAGE>;
		channel = <0>;
		voltage = <984126>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	broken-cd;
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_4bit_pc_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	broken-cd;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

#include "axp209.dtsi"

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_ldo3 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "csi-1.8v";
};

&reg_ldo4 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-name = "csi-2.8v";
};

&reg_usb0_vbus {
	gpio = <&pio 2 12 GPIO_ACTIVE_HIGH>; /* PC12 */
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 2 GPIO_ACTIVE_HIGH>; /* PG2 */
	usb0_vbus_det-gpios = <&pio 6 1 GPIO_ACTIVE_HIGH>; /* PG1 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_vcc5v0>;
	status = "okay";
};
