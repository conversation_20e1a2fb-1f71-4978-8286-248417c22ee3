/*
 * Copyright 2012 <PERSON><PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun5i-a13.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "Olimex A13-Olinuxino";
	compatible = "olimex,a13-olinuxino", "allwinner,sun5i-a13";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&led_pins_olinuxino>;

		led {
			gpios = <&pio 6 9 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

	bridge {
		compatible = "dumb-vga-dac";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				vga_bridge_in: endpoint {
					remote-endpoint = <&tcon0_out_vga>;
				};
			};

			port@1 {
				reg = <1>;

				vga_bridge_out: endpoint {
					remote-endpoint = <&vga_con_in>;
				};
			};
		};
	};

	vga {
		compatible = "vga-connector";

		port {
			vga_con_in: endpoint {
				remote-endpoint = <&vga_bridge_out>;
			};
		};
	};
};

&be0 {
	status = "okay";
};

&codec {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		compatible = "x-powers,axp209";
		reg = <0x34>;
		interrupts = <0>;

		interrupt-controller;
		#interrupt-cells = <1>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&lradc {
	vref-supply = <&reg_vcc3v0>;
	status = "okay";

	button-191 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <191274>;
	};

	button-392 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <392644>;
	};

	button-601 {
		label = "Menu";
		linux,code = <KEY_MENU>;
		channel = <0>;
		voltage = <601151>;
	};

	button-795 {
		label = "Enter";
		linux,code = <KEY_ENTER>;
		channel = <0>;
		voltage = <795090>;
	};

	button-987 {
		label = "Home";
		linux,code = <KEY_HOMEPAGE>;
		channel = <0>;
		voltage = <987387>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 6 0 GPIO_ACTIVE_LOW>; /* PG0 */
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pio {
	led_pins_olinuxino: led-pin {
		pins = "PG9";
		function = "gpio_out";
		drive-strength = <20>;
	};
};

&reg_usb0_vbus {
	status = "okay";
	gpio = <&pio 6 12 GPIO_ACTIVE_HIGH>; /* PG12 */
};

&reg_usb1_vbus {
	gpio = <&pio 6 11 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&tcon0 {
	pinctrl-names = "default";
	pinctrl-0 = <&lcd_rgb666_pins>;
	status = "okay";
};

&tcon0_out {
	tcon0_out_vga: endpoint@0 {
		reg = <0>;
		remote-endpoint = <&vga_bridge_in>;
	};
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 2 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PG2 */
	usb0_vbus_det-gpios = <&pio 6 1 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>; /* PG1 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};
