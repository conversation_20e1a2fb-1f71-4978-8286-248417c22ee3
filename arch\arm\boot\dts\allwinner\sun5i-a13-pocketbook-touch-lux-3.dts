// SPDX-License-Identifier: GPL-2.0 OR MIT
/*
 * Copyright 2019 Ondre<PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun5i-a13.dtsi"
#include "sunxi-common-regulators.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "PocketBook Touch Lux 3";
	compatible = "pocketbook,touch-lux-3", "allwinner,sun5i-a13";

	aliases {
		serial0 = &uart1;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
	};

	backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm 0 50000 PWM_POLARITY_INVERTED>;
		enable-gpios = <&pio 1 4 GPIO_ACTIVE_HIGH>; /* PB4 */
		brightness-levels = <0 10 20 30 40 50 60 70 80 90 100>;
		default-brightness-level = <8>;
		power-supply = <&reg_vcc3v3>;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led {
			gpios = <&pio 4 8 GPIO_ACTIVE_LOW>; /* PE8 */
			default-state = "on";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		label = "GPIO Keys";

		key-right {
			label = "Right";
			linux,code = <KEY_RIGHT>;
			gpios = <&pio 6 9 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PG9 */
		};

		key-left {
			label = "Left";
			linux,code = <KEY_LEFT>;
			gpios = <&pio 6 10 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>; /* PG10 */
		};
	};

	reg_1v8: regulator-1v8 {
		compatible = "regulator-fixed";
		regulator-name = "vdd-1v8-nor-ctp";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		gpio = <&pio 2 15 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	reg_1v8_nor: regulator-nor {
		compatible = "regulator-fixed";
		regulator-name = "vdd-nor";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		gpio = <&pio 2 14 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&reg_1v8>;
		regulator-always-on;
	};

	reg_1v8_ctp: regulator-ctp {
		compatible = "regulator-fixed";
		regulator-name = "vdd-ctp";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		gpio = <&pio 2 13 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&reg_1v8>;
	};

	reg_3v3_mmc0: regulator-mmc0 {
		compatible = "regulator-fixed";
		regulator-name = "vdd-mmc0";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pio 4 4 GPIO_ACTIVE_LOW>; /* PE4 */
		vin-supply = <&reg_vcc3v3>;
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupts = <0>;
	};
};

#include "axp209.dtsi"

&i2c1 {
	status = "okay";

	pcf8563: rtc@51 {
		compatible = "nxp,pcf8563";
		reg = <0x51>;
	};
};

&i2c2 {
	status = "okay";

	/* Touchpanel is connected here. */
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-200 {
		label = "Home";
		linux,code = <KEY_HOME>;
		channel = <0>;
		voltage = <200000>;
	};

	button-400 {
		label = "Menu";
		linux,code = <KEY_MENU>;
		channel = <0>;
		voltage = <400000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_3v3_mmc0>;
	bus-width = <4>;
	cd-gpios = <&pio 6 0 GPIO_ACTIVE_LOW>; /* PG0 */
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_4bit_pc_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pin>;
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1200000>;
	regulator-name = "vdd-int-pll";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_ldo3 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi";
	/* We need this otherwise the LDO3 would overload */
	regulator-soft-start;
	regulator-ramp-delay = <1600>;
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pe_pins>, <&spi2_cs0_pe_pin>;
	status = "okay";

	epd_flash: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "macronix,mx25u4033", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <4000000>;
	};
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb1_vbus-supply = <&reg_ldo3>;
	status = "okay";
};
