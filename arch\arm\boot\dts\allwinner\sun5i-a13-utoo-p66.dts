/*
 * Copyright 2015 <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun5i-a13.dtsi"
#include "sun5i-reference-design-tablet.dtsi"
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "Utoo P66";
	compatible = "utoo,p66", "allwinner,sun5i-a13";

	/* The P66 uses the uart pins as gpios */
	aliases {
		/delete-property/serial0;
	};

	chosen {
		/delete-property/stdout-path;
	};

	i2c_lcd: i2c {
		/* The lcd panel i2c interface is hooked up via gpios */
		compatible = "i2c-gpio";
		sda-gpios = <&pio 6 12 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PG12 */
		scl-gpios = <&pio 6 10 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PG10 */
		i2c-gpio,delay-us = <5>;
	};
};

&backlight {
	/* Note levels of 10 / 20% result in backlight off */
	brightness-levels = <0 30 40 50 60 70 80 90 100>;
	default-brightness-level = <6>;
};

&codec {
	allwinner,pa-gpios = <&pio 6 3 GPIO_ACTIVE_HIGH>; /* PG3 */
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <8>;
	non-removable;
	status = "okay";

	mmccard: mmccard@0 {
		reg = <0>;
		compatible = "mmc-card";
		broken-hpi;
	};
};

&reg_usb0_vbus {
	gpio = <&pio 1 4 GPIO_ACTIVE_HIGH>; /* PB4 */
};

&touchscreen {
	compatible = "chipone,icn8318";
	reg = <0x40>;
	/* The P66 uses a different EINT then the reference design */
	interrupts = <6 9 IRQ_TYPE_EDGE_FALLING>; /* EINT9 (PG9) */
	/* The icn8318 binding expects wake-gpios instead of power-gpios */
	wake-gpios = <&pio 1 3 GPIO_ACTIVE_HIGH>; /* PB3 */
	touchscreen-size-x = <800>;
	touchscreen-size-y = <480>;
	touchscreen-inverted-x;
	touchscreen-swapped-x-y;
	status = "okay";
};

&uart1 {
	/* The P66 uses the uart pins as gpios */
	status = "disabled";
};
