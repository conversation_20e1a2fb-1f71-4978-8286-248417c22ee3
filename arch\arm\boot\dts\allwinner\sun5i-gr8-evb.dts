/*
 * Copyright 2016 Free Electrons
 * Copyright 2016 NextThing Co
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun5i-gr8.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "NextThing GR8-EVB";
	compatible = "nextthing,gr8-evb", "nextthing,gr8";

	aliases {
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		serial0 = &uart1;
		serial1 = &uart2;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm 0 10000 0>;
		enable-gpios = <&axp_gpio 1 GPIO_ACTIVE_HIGH>;
		power-supply = <&reg_vcc3v3>;
		brightness-levels = <0 10 20 30 40 50 60 70 80 90 100>;
		default-brightness-level = <8>;
	};

	sound-analog {
		compatible = "simple-audio-card";
		simple-audio-card,name = "gr8-evb-wm8978";
		simple-audio-card,format = "i2s";
		simple-audio-card,mclk-fs = <512>;

		simple-audio-card,cpu {
			sound-dai = <&i2s0>;
		};

		simple-audio-card,codec {
			sound-dai = <&wm8978>;
		};
	};

	sound-spdif {
		compatible = "simple-audio-card";
		simple-audio-card,name = "On-board SPDIF";

		simple-audio-card,cpu {
			sound-dai = <&spdif>;
		};

		simple-audio-card,codec {
			sound-dai = <&spdif_out>;
		};
	};

	spdif_out: spdif-out {
		#sound-dai-cells = <0>;
		compatible = "linux,spdif-dit";
	};
};

&be0 {
	status = "okay";
};

&codec {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;

		/*
		* The interrupt is routed through the "External Fast
		* Interrupt Request" pin (ball G13 of the module)
		* directly to the main interrupt controller, without
		* any other controller interfering.
		*/
		interrupts = <0>;
	};
};

#include "axp209.dtsi"

&i2c1 {
	status = "okay";

	wm8978: codec@1a {
		#sound-dai-cells = <0>;
		compatible = "wlf,wm8978";
		reg = <0x1a>;
	};

	pcf8563: rtc@51 {
		compatible = "nxp,pcf8563";
		reg = <0x51>;
	};
};

&i2c2 {
	status = "okay";
};

&i2s0 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2s0_mclk_pin>, <&i2s0_data_pins>;
	status = "okay";
};

&ir0 {
	pinctrl-names = "default";
	pinctrl-0 = <&ir0_rx_pin>;
	status = "okay";
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-190 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <190000>;
	};

	button-390 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <390000>;
	};

	button-600 {
		label = "Menu";
		linux,code = <KEY_MENU>;
		channel = <0>;
		voltage = <600000>;
	};

	button-800 {
		label = "Search";
		linux,code = <KEY_SEARCH>;
		channel = <0>;
		voltage = <800000>;
	};

	button-980 {
		label = "Home";
		linux,code = <KEY_HOMEPAGE>;
		channel = <0>;
		voltage = <980000>;
	};

	button-1180 {
		label = "Esc";
		linux,code = <KEY_ESC>;
		channel = <0>;
		voltage = <1180000>;
	};

	button-1400 {
		label = "Enter";
		linux,code = <KEY_ENTER>;
		channel = <0>;
		voltage = <1400000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 6 0 GPIO_ACTIVE_LOW>; /* PG0 */
	status = "okay";
};

&nfc {
	pinctrl-names = "default";
	pinctrl-0 = <&nand_pins &nand_cs0_pin &nand_rb0_pin>;

	/* MLC Support sucks for now */
	status = "disabled";
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pin>;
	status = "okay";
};

&reg_dcdc2 {
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
	regulator-always-on;
};

&reg_dcdc3 {
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1300000>;
	regulator-name = "vdd-sys";
	regulator-always-on;
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-min-microvolt = <2700000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "avcc";
	regulator-always-on;
};

&reg_usb1_vbus {
	gpio = <&pio 6 13 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&rtp {
	allwinner,ts-attached;
};

&spdif {
	pinctrl-names = "default";
	pinctrl-0 = <&spdif_tx_pin>;
	status = "okay";
};

&tve0 {
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>, <&uart1_cts_rts_pins>;
	status = "okay";
};

&usb_otg {
	/*
	 * The GR8-EVB has a somewhat interesting design. There's a
	 * pin supposed to control VBUS, an ID pin, a VBUS detect pin,
	 * so everything should work just fine.
	 *
	 * Except that the pin supposed to control VBUS is not
	 * connected to any controllable output, neither to the SoC
	 * through a GPIO or to the PMIC, and it is pulled down,
	 * meaning that we will never be able to enable VBUS on this
	 * board.
	 */
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 6 2 GPIO_ACTIVE_HIGH>; /* PG2 */
	usb0_vbus_det-gpios = <&pio 6 1 GPIO_ACTIVE_HIGH>; /* PG1 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};
