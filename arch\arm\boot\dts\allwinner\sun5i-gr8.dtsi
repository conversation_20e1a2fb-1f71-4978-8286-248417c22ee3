/*
 * Copyright 2016 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This library is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This library is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "sun5i.dtsi"

#include <dt-bindings/clock/sun5i-ccu.h>
#include <dt-bindings/dma/sun4i-a10.h>
#include <dt-bindings/reset/sun5i-ccu.h>

/ {
	display-engine {
		compatible = "allwinner,sun5i-a13-display-engine";
		allwinner,pipelines = <&fe0>;
	};

	soc {
		pwm: pwm@1c20e00 {
			compatible = "allwinner,sun5i-a10s-pwm";
			reg = <0x01c20e00 0xc>;
			clocks = <&ccu CLK_HOSC>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		spdif: spdif@1c21000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun4i-a10-spdif";
			reg = <0x01c21000 0x400>;
			interrupts = <13>;
			clocks = <&ccu CLK_APB0_SPDIF>, <&ccu CLK_SPDIF>;
			clock-names = "apb", "spdif";
			dmas = <&dma SUN4I_DMA_NORMAL 2>,
			       <&dma SUN4I_DMA_NORMAL 2>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s0: i2s@1c22400 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun4i-a10-i2s";
			reg = <0x01c22400 0x400>;
			interrupts = <16>;
			clocks = <&ccu CLK_APB0_I2S>, <&ccu CLK_I2S>;
			clock-names = "apb", "mod";
			dmas = <&dma SUN4I_DMA_NORMAL 3>,
			       <&dma SUN4I_DMA_NORMAL 3>;
			dma-names = "rx", "tx";
			status = "disabled";
		};
	};
};

&ccu {
	compatible = "nextthing,gr8-ccu";
};

&pio {
	compatible = "nextthing,gr8-pinctrl";

	i2s0_data_pins: i2s0-data-pins {
		pins = "PB6", "PB7", "PB8", "PB9";
		function = "i2s0";
	};

	i2s0_mclk_pin: i2s0-mclk-pin {
		pins = "PB5";
		function = "i2s0";
	};

	pwm1_pins: pwm1-pin {
		pins = "PG13";
		function = "pwm1";
	};

	spdif_tx_pin: spdif-tx-pin {
		pins = "PB10";
		function = "spdif";
		bias-pull-up;
	};

	uart1_cts_rts_pins: uart1-cts-rts-pins {
		pins = "PG5", "PG6";
		function = "uart1";
	};
};
