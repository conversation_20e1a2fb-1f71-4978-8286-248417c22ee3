/*
 * Copyright 2015 Free Electrons
 * Copyright 2015 NextThing Co
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun5i-r8.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "NextThing C.H.I.P.";
	compatible = "nextthing,chip", "allwinner,sun5i-r8", "allwinner,sun5i-a13";

	aliases {
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		serial0 = &uart1;
		serial1 = &uart3;
		spi0 = &spi2;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "chip:white:status";
			gpios = <&axp_gpio 2 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

	mmc0_pwrseq: mmc0_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 2 19 GPIO_ACTIVE_LOW>; /* PC19 */
	};

	onewire {
		compatible = "w1-gpio";
		gpios = <&pio 3 2 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PD2 */
	};
};

&be0 {
	status = "okay";
};

&codec {
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&ehci0 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;

		/*
		 * The interrupt is routed through the "External Fast
		 * Interrupt Request" pin (ball G13 of the module)
		 * directly to the main interrupt controller, without
		 * any other controller interfering.
		 */
		interrupts = <0>;
	};
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&i2c1 {
	status = "disabled";
};

&i2c2 {
	status = "okay";

	xio: gpio@38 {
		compatible = "nxp,pcf8574a";
		reg = <0x38>;

		gpio-controller;
		#gpio-cells = <2>;

		interrupt-parent = <&pio>;
		interrupts = <6 0 IRQ_TYPE_EDGE_FALLING>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};
};

&mmc0_pins {
	bias-pull-up;
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	mmc-pwrseq = <&mmc0_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&reg_dcdc2 {
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "cpuvdd";
	regulator-always-on;
};

&reg_dcdc3 {
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1300000>;
	regulator-name = "corevdd";
	regulator-always-on;
};

&reg_ldo1 {
	regulator-name = "rtcvdd";
};

&reg_ldo2 {
	regulator-min-microvolt = <2700000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "avcc";
	regulator-always-on;
};

/*
 * Both LDO3 and LDO4 are used in parallel to power up the WiFi/BT
 * Chip.
 *
 * If those are not enabled, the SDIO part will not enumerate, and
 * since there's no way currently to pass DT infos to an SDIO device,
 * we cannot really do better than this ugly hack for now.
 */
&reg_ldo3 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi-1";
	regulator-always-on;
};

&reg_ldo4 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi-2";
	regulator-always-on;
};

&reg_ldo5 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "vcc-1v8";
};

&reg_usb0_vbus {
	vin-supply = <&reg_vcc5v0>;
	gpio = <&pio 1 10 GPIO_ACTIVE_HIGH>; /* PB10 */
	status = "okay";
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pe_pins>;
	status = "disabled";
};

&tcon0 {
	status = "okay";
};

&tve0 {
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pg_pins>,
		    <&uart3_cts_rts_pg_pins>;
	status = "okay";

	bluetooth {
		compatible = "realtek,rtl8723bs-bt";
		device-wake-gpios = <&axp_gpio 3 GPIO_ACTIVE_HIGH>;
		host-wake-gpios = <&pio 1 3 GPIO_ACTIVE_HIGH>; /* PB3 */
	};
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	status = "okay";

	usb0_id_det-gpios = <&pio 6 2 GPIO_ACTIVE_HIGH>; /* PG2 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_vcc5v0>;
};
