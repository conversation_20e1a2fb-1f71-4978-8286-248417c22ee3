/*
 * Copyright 2012-2015 <PERSON><PERSON> R<PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This library is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This library is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/clock/sun5i-ccu.h>
#include <dt-bindings/dma/sun4i-a10.h>
#include <dt-bindings/reset/sun5i-ccu.h>

/ {
	interrupt-parent = <&intc>;
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a8";
			reg = <0x0>;
			clocks = <&ccu CLK_CPU>;
		};
	};

	chosen {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		framebuffer-lcd0 {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_be0-lcd0";
			clocks = <&ccu CLK_AHB_LCD>, <&ccu CLK_AHB_DE_BE>, <&ccu CLK_DE_BE>,
				 <&ccu CLK_TCON_CH0>, <&ccu CLK_DRAM_DE_BE>;
			status = "disabled";
		};

		framebuffer-lcd0-tve0 {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_be0-lcd0-tve0";
			clocks = <&ccu CLK_AHB_TVE>, <&ccu CLK_AHB_LCD>,
				 <&ccu CLK_AHB_DE_BE>, <&ccu CLK_DE_BE>,
				 <&ccu CLK_TCON_CH1>, <&ccu CLK_DRAM_DE_BE>;
			status = "disabled";
		};
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: clk-24M {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-output-names = "osc24M";
		};

		osc32k: clk-32k {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-output-names = "osc32k";
		};
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		/* Address must be kept in the lower 256 MiBs of DRAM for VE. */
		default-pool {
			compatible = "shared-dma-pool";
			size = <0x6000000>;
			alloc-ranges = <0x40000000 0x10000000>;
			reusable;
			linux,cma-default;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		dma-ranges;
		ranges;

		system-control@1c00000 {
			compatible = "allwinner,sun5i-a13-system-control";
			reg = <0x01c00000 0x30>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			sram_a: sram@0 {
				compatible = "mmio-sram";
				reg = <0x00000000 0xc000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x00000000 0xc000>;

				emac_sram: sram-section@8000 {
					compatible = "allwinner,sun5i-a13-sram-a3-a4",
						     "allwinner,sun4i-a10-sram-a3-a4";
					reg = <0x8000 0x4000>;
					status = "disabled";
				};
			};

			sram_d: sram@10000 {
				compatible = "mmio-sram";
				reg = <0x00010000 0x1000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x00010000 0x1000>;

				otg_sram: sram-section@0 {
					compatible = "allwinner,sun5i-a13-sram-d",
						     "allwinner,sun4i-a10-sram-d";
					reg = <0x0000 0x1000>;
					status = "disabled";
				};
			};

			sram_c: sram@1d00000 {
				compatible = "mmio-sram";
				reg = <0x01d00000 0xd0000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x01d00000 0xd0000>;

				ve_sram: sram-section@0 {
					compatible = "allwinner,sun5i-a13-sram-c1",
						     "allwinner,sun4i-a10-sram-c1";
					reg = <0x000000 0x80000>;
				};
			};
		};

		mbus: dram-controller@1c01000 {
			compatible = "allwinner,sun5i-a13-mbus";
			reg = <0x01c01000 0x1000>;
			clocks = <&ccu CLK_MBUS>;
			#address-cells = <1>;
			#size-cells = <1>;
			dma-ranges = <0x00000000 0x40000000 0x20000000>;
			#interconnect-cells = <1>;
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun4i-a10-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <27>;
			clocks = <&ccu CLK_AHB_DMA>;
			#dma-cells = <2>;
		};

		nfc: nand-controller@1c03000 {
			compatible = "allwinner,sun4i-a10-nand";
			reg = <0x01c03000 0x1000>;
			interrupts = <37>;
			clocks = <&ccu CLK_AHB_NAND>, <&ccu CLK_NAND>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 3>;
			dma-names = "rxtx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi0: spi@1c05000 {
			compatible = "allwinner,sun4i-a10-spi";
			reg = <0x01c05000 0x1000>;
			interrupts = <10>;
			clocks = <&ccu CLK_AHB_SPI0>, <&ccu CLK_SPI0>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 27>,
			       <&dma SUN4I_DMA_DEDICATED 26>;
			dma-names = "rx", "tx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi1: spi@1c06000 {
			compatible = "allwinner,sun4i-a10-spi";
			reg = <0x01c06000 0x1000>;
			interrupts = <11>;
			clocks = <&ccu CLK_AHB_SPI1>, <&ccu CLK_SPI1>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 9>,
			       <&dma SUN4I_DMA_DEDICATED 8>;
			dma-names = "rx", "tx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		tve0: tv-encoder@1c0a000 {
			compatible = "allwinner,sun4i-a10-tv-encoder";
			reg = <0x01c0a000 0x1000>;
			clocks = <&ccu CLK_AHB_TVE>;
			resets = <&ccu RST_TVE>;
			status = "disabled";

			port {

				tve0_in_tcon0: endpoint {
					remote-endpoint = <&tcon0_out_tve0>;
				};
			};
		};

		emac: ethernet@1c0b000 {
			compatible = "allwinner,sun4i-a10-emac";
			reg = <0x01c0b000 0x1000>;
			interrupts = <55>;
			clocks = <&ccu CLK_AHB_EMAC>;
			allwinner,sram = <&emac_sram 1>;
			status = "disabled";
		};

		mdio: mdio@1c0b080 {
			compatible = "allwinner,sun4i-a10-mdio";
			reg = <0x01c0b080 0x14>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		tcon0: lcd-controller@1c0c000 {
			compatible = "allwinner,sun5i-a13-tcon";
			reg = <0x01c0c000 0x1000>;
			interrupts = <44>;
			dmas = <&dma SUN4I_DMA_DEDICATED 14>;
			resets = <&ccu RST_LCD>;
			reset-names = "lcd";
			clocks = <&ccu CLK_AHB_LCD>,
				 <&ccu CLK_TCON_CH0>,
				 <&ccu CLK_TCON_CH1>;
			clock-names = "ahb",
				      "tcon-ch0",
				      "tcon-ch1";
			clock-output-names = "tcon-data-clock";
			#clock-cells = <0>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					reg = <0>;

					tcon0_in_be0: endpoint {
						remote-endpoint = <&be0_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon0_out_tve0: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tve0_in_tcon0>;
						allwinner,tcon-channel = <1>;
					};
				};
			};
		};

		video-codec@1c0e000 {
			compatible = "allwinner,sun5i-a13-video-engine";
			reg = <0x01c0e000 0x1000>;
			clocks = <&ccu CLK_AHB_VE>, <&ccu CLK_VE>,
				 <&ccu CLK_DRAM_VE>;
			clock-names = "ahb", "mod", "ram";
			resets = <&ccu RST_VE>;
			interrupts = <53>;
			allwinner,sram = <&ve_sram 1>;
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun5i-a13-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_AHB_MMC0>, <&ccu CLK_MMC0>;
			clock-names = "ahb", "mmc";
			interrupts = <32>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun5i-a13-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_AHB_MMC1>, <&ccu CLK_MMC1>;
			clock-names = "ahb", "mmc";
			interrupts = <33>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun5i-a13-mmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&ccu CLK_AHB_MMC2>, <&ccu CLK_MMC2>;
			clock-names = "ahb", "mmc";
			interrupts = <34>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		usb_otg: usb@1c13000 {
			compatible = "allwinner,sun4i-a10-musb";
			reg = <0x01c13000 0x0400>;
			clocks = <&ccu CLK_AHB_OTG>;
			interrupts = <38>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			allwinner,sram = <&otg_sram 1>;
			dr_mode = "otg";
			status = "disabled";
		};

		usbphy: phy@1c13400 {
			#phy-cells = <1>;
			compatible = "allwinner,sun5i-a13-usb-phy";
			reg = <0x01c13400 0x10>, <0x01c14800 0x4>;
			reg-names = "phy_ctrl", "pmu1";
			clocks = <&ccu CLK_USB_PHY0>;
			clock-names = "usb_phy";
			resets = <&ccu RST_USB_PHY0>, <&ccu RST_USB_PHY1>;
			reset-names = "usb0_reset", "usb1_reset";
			status = "disabled";
		};

		ehci0: usb@1c14000 {
			compatible = "allwinner,sun5i-a13-ehci", "generic-ehci";
			reg = <0x01c14000 0x100>;
			interrupts = <39>;
			clocks = <&ccu CLK_AHB_EHCI>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci0: usb@1c14400 {
			compatible = "allwinner,sun5i-a13-ohci", "generic-ohci";
			reg = <0x01c14400 0x100>;
			interrupts = <40>;
			clocks = <&ccu CLK_USB_OHCI>, <&ccu CLK_AHB_OHCI>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		crypto: crypto-engine@1c15000 {
			compatible = "allwinner,sun5i-a13-crypto",
				     "allwinner,sun4i-a10-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <54>;
			clocks = <&ccu CLK_AHB_SS>, <&ccu CLK_SS>;
			clock-names = "ahb", "mod";
		};

		spi2: spi@1c17000 {
			compatible = "allwinner,sun4i-a10-spi";
			reg = <0x01c17000 0x1000>;
			interrupts = <12>;
			clocks = <&ccu CLK_AHB_SPI2>, <&ccu CLK_SPI2>;
			clock-names = "ahb", "mod";
			dmas = <&dma SUN4I_DMA_DEDICATED 29>,
			       <&dma SUN4I_DMA_DEDICATED 28>;
			dma-names = "rx", "tx";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		ccu: clock@1c20000 {
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&osc32k>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		intc: interrupt-controller@1c20400 {
			compatible = "allwinner,sun4i-a10-ic";
			reg = <0x01c20400 0x400>;
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			reg = <0x01c20800 0x400>;
			interrupts = <28>;
			clocks = <&ccu CLK_APB0_PIO>, <&osc24M>, <&osc32k>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			emac_pd_pins: emac-pd-pins {
				pins = "PD6", "PD7", "PD10",
				       "PD11", "PD12", "PD13", "PD14",
				       "PD15", "PD18", "PD19", "PD20",
				       "PD21", "PD22", "PD23", "PD24",
				       "PD25", "PD26", "PD27";
				function = "emac";
			};

			i2c0_pins: i2c0-pins {
				pins = "PB0", "PB1";
				function = "i2c0";
			};

			i2c1_pins: i2c1-pins {
				pins = "PB15", "PB16";
				function = "i2c1";
			};

			i2c2_pins: i2c2-pins {
				pins = "PB17", "PB18";
				function = "i2c2";
			};

			ir0_rx_pin: ir0-rx-pin {
				pins = "PB4";
				function = "ir0";
			};

			lcd_rgb565_pins: lcd-rgb565-pins {
				pins = "PD3", "PD4", "PD5", "PD6", "PD7",
						 "PD10", "PD11", "PD12", "PD13", "PD14", "PD15",
						 "PD19", "PD20", "PD21", "PD22", "PD23",
						 "PD24", "PD25", "PD26", "PD27";
				function = "lcd0";
			};

			lcd_rgb666_pins: lcd-rgb666-pins {
				pins = "PD2", "PD3", "PD4", "PD5", "PD6", "PD7",
				       "PD10", "PD11", "PD12", "PD13", "PD14", "PD15",
				       "PD18", "PD19", "PD20", "PD21", "PD22", "PD23",
				       "PD24", "PD25", "PD26", "PD27";
				function = "lcd0";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2", "PF3",
				       "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_4bit_pc_pins: mmc2-4bit-pc-pins {
				pins = "PC6", "PC7", "PC8", "PC9",
				       "PC10", "PC11";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			/omit-if-no-ref/
			mmc2_4bit_pe_pins: mmc2-4bit-pe-pins {
				pins = "PE4", "PE5", "PE6", "PE7",
				       "PE8", "PE9";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_8bit_pins: mmc2-8bit-pins {
				pins = "PC6", "PC7", "PC8", "PC9",
				       "PC10", "PC11", "PC12", "PC13",
				       "PC14", "PC15";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			nand_pins: nand-pins {
				pins = "PC0", "PC1", "PC2",
				       "PC5", "PC8", "PC9", "PC10",
				       "PC11", "PC12", "PC13", "PC14",
				       "PC15";
				function = "nand0";
			};

			nand_cs0_pin: nand-cs0-pin {
				pins = "PC4";
				function = "nand0";
			};

			nand_rb0_pin: nand-rb0-pin {
				pins = "PC6";
				function = "nand0";
			};

			pwm0_pin: pwm0-pin {
				pins = "PB2";
				function = "pwm";
			};

			spi2_pe_pins: spi2-pe-pins {
				pins = "PE1", "PE2", "PE3";
				function = "spi2";
			};

			spi2_cs0_pe_pin: spi2-cs0-pe-pin {
				pins = "PE0";
				function = "spi2";
			};

			uart1_pe_pins: uart1-pe-pins {
				pins = "PE10", "PE11";
				function = "uart1";
			};

			uart1_pg_pins: uart1-pg-pins {
				pins = "PG3", "PG4";
				function = "uart1";
			};

			uart2_pd_pins: uart2-pd-pins {
				pins = "PD2", "PD3";
				function = "uart2";
			};

			uart2_cts_rts_pd_pins: uart2-cts-rts-pd-pins {
				pins = "PD4", "PD5";
				function = "uart2";
			};

			uart3_pg_pins: uart3-pg-pins {
				pins = "PG9", "PG10";
				function = "uart3";
			};

			uart3_cts_rts_pg_pins: uart3-cts-rts-pg-pins {
				pins = "PG11", "PG12";
				function = "uart3";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun4i-a10-timer";
			reg = <0x01c20c00 0x90>;
			interrupts = <22>,
				     <23>,
				     <24>,
				     <25>,
				     <67>,
				     <68>;
			clocks = <&ccu CLK_HOSC>;
		};

		wdt: watchdog@1c20c90 {
			compatible = "allwinner,sun4i-a10-wdt";
			reg = <0x01c20c90 0x10>;
			interrupts = <24>;
			clocks = <&osc24M>;
		};

		ir0: ir@1c21800 {
			compatible = "allwinner,sun4i-a10-ir";
			clocks = <&ccu CLK_APB0_IR>, <&ccu CLK_IR>;
			clock-names = "apb", "ir";
			interrupts = <5>;
			reg = <0x01c21800 0x40>;
			status = "disabled";
		};

		lradc: lradc@1c22800 {
			compatible = "allwinner,sun4i-a10-lradc-keys";
			reg = <0x01c22800 0x100>;
			interrupts = <31>;
			status = "disabled";
		};

		codec: codec@1c22c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun4i-a10-codec";
			reg = <0x01c22c00 0x40>;
			interrupts = <30>;
			clocks = <&ccu CLK_APB0_CODEC>, <&ccu CLK_CODEC>;
			clock-names = "apb", "codec";
			dmas = <&dma SUN4I_DMA_NORMAL 19>,
			       <&dma SUN4I_DMA_NORMAL 19>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		sid: eeprom@1c23800 {
			compatible = "allwinner,sun4i-a10-sid";
			reg = <0x01c23800 0x10>;
		};

		rtp: rtp@1c25000 {
			compatible = "allwinner,sun5i-a13-ts";
			reg = <0x01c25000 0x100>;
			interrupts = <29>;
			#thermal-sensor-cells = <0>;
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <1>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART0>;
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <2>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART1>;
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <3>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART2>;
			status = "disabled";
		};

		uart3: serial@1c28c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28c00 0x400>;
			interrupts = <4>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB1_UART3>;
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun4i-a10-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <7>;
			clocks = <&ccu CLK_APB1_I2C0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun4i-a10-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <8>;
			clocks = <&ccu CLK_APB1_I2C1>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@1c2b400 {
			compatible = "allwinner,sun4i-a10-i2c";
			reg = <0x01c2b400 0x400>;
			interrupts = <9>;
			clocks = <&ccu CLK_APB1_I2C2>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mali: gpu@1c40000 {
			compatible = "allwinner,sun4i-a10-mali", "arm,mali-400";
			reg = <0x01c40000 0x10000>;
			interrupts = <69>, <70>, <71>, <72>,  <73>;
			interrupt-names = "gp", "gpmmu", "pp0", "ppmmu0", "pmu";
			clocks = <&ccu CLK_AHB_GPU>, <&ccu CLK_GPU>;
			clock-names = "bus", "core";
			resets = <&ccu RST_GPU>;
			assigned-clocks = <&ccu CLK_GPU>;
			assigned-clock-rates = <320000000>;
		};

		timer@1c60000 {
			compatible = "allwinner,sun5i-a13-hstimer";
			reg = <0x01c60000 0x1000>;
			interrupts = <82>, <83>;
			clocks = <&ccu CLK_AHB_HSTIMER>;
		};

		fe0: display-frontend@1e00000 {
			compatible = "allwinner,sun5i-a13-display-frontend";
			reg = <0x01e00000 0x20000>;
			interrupts = <47>;
			clocks = <&ccu CLK_DE_FE>, <&ccu CLK_DE_FE>,
				 <&ccu CLK_DRAM_DE_FE>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_DE_FE>;
			interconnects = <&mbus 19>;
			interconnect-names = "dma-mem";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe0_out: port@1 {
					reg = <1>;

					fe0_out_be0: endpoint {
						remote-endpoint = <&be0_in_fe0>;
					};
				};
			};
		};

		be0: display-backend@1e60000 {
			compatible = "allwinner,sun5i-a13-display-backend";
			reg = <0x01e60000 0x10000>;
			interrupts = <47>;
			clocks = <&ccu CLK_AHB_DE_BE>, <&ccu CLK_DE_BE>,
				 <&ccu CLK_DRAM_DE_BE>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_DE_BE>;
			interconnects = <&mbus 18>;
			interconnect-names = "dma-mem";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be0_in: port@0 {
					reg = <0>;

					be0_in_fe0: endpoint {
						remote-endpoint = <&fe0_out_be0>;
					};
				};

				be0_out: port@1 {
					reg = <1>;

					be0_out_tcon0: endpoint {
						remote-endpoint = <&tcon0_in_be0>;
					};
				};
			};
		};
	};
};
