/*
 * Copyright 2013 <PERSON><PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun6i-a31.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "WITS A31 Colombus Evaluation Board";
	compatible = "wits,colombus", "allwinner,sun6i-a31";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	i2c_lcd: i2c {
		/* The lcd panel i2c interface is hooked up via gpios */
		compatible = "i2c-gpio";
		sda-gpios = <&pio 0 23 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PA23 */
		scl-gpios = <&pio 0 24 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PA24 */
		i2c-gpio,delay-us = <5>;
	};
};

&ehci1 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "rgmii";
	status = "okay";
};

&i2c0 {
	status = "fail";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	mma8452: mma8452@1d {
		compatible = "fsl,mma8452";
		reg = <0x1d>;
		interrupt-parent = <&pio>;
		interrupts = <0 9 IRQ_TYPE_LEVEL_LOW>; /* PA9 */
	};
};

&mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v0>;
	bus-width = <4>;
	cd-gpios = <&pio 0 8 GPIO_ACTIVE_LOW>; /* PA8 */
	status = "okay";
};

&reg_usb2_vbus {
	gpio = <&pio 7 24 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_ph_pins>;
	status = "okay";
};

&usbphy {
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
