/*
 * Copyright 2014 <PERSON><PERSON>ard
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun6i-a31.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Merrii A31 Hummingbird";
	compatible = "merrii,a31-hummingbird", "allwinner,sun6i-a31";

	aliases {
		rtc0 = &pcf8563;
		rtc1 = &rtc;
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	hdmi-connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	vga-connector {
		compatible = "vga-connector";

		port {
			vga_con_in: endpoint {
				remote-endpoint = <&vga_dac_out>;
			};
		};
	};

	vga-dac {
		compatible = "dumb-vga-dac";
		vdd-supply = <&reg_vga_3v3>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				vga_dac_in: endpoint {
					remote-endpoint = <&tcon0_out_vga>;
				};
			};

			port@1 {
				reg = <1>;

				vga_dac_out: endpoint {
					remote-endpoint = <&vga_con_in>;
				};
			};
		};
	};

	reg_vga_3v3: vga_3v3_regulator {
		compatible = "regulator-fixed";
		regulator-name = "vga-3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		enable-active-high;
		gpio = <&pio 7 25 GPIO_ACTIVE_HIGH>; /* PH25 */
	};

	wifi_pwrseq: wifi_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 6 10 GPIO_ACTIVE_LOW>; /* PG10 */
	};
};

&codec {
	allwinner,audio-routing =
		"Headphone", "HP",
		"Speaker", "LINEOUT",
		"LINEIN", "Line In",
		"MIC1", "Mic",
		"MIC2", "Headset Mic",
		"Mic",	"MBIAS",
		"Headset Mic", "HBIAS";
	allwinner,pa-gpios = <&pio 7 22 GPIO_ACTIVE_HIGH>; /* PH22 */
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc3>;
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "rgmii-id";
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c0 {
	/* pull-ups and devices require AXP221 DLDO3 */
	status = "failed";
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	pcf8563: rtc@51 {
		compatible = "nxp,pcf8563";
		reg = <0x51>;
	};
};

&ir {
	pinctrl-names = "default";
	pinctrl-0 = <&s_ir_rx_pin>;
	status = "okay";
};

&mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
		reset-gpios = <&pio 0 21 GPIO_ACTIVE_LOW>;
		reset-assert-us = <10000>;
		reset-deassert-us = <30000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 0 8 GPIO_ACTIVE_LOW>; /* PA8 */
	status = "okay";
};

&mmc1 {
	vmmc-supply = <&reg_aldo1>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&p2wi {
	status = "okay";

	axp22x: pmic@68 {
		compatible = "x-powers,axp221";
		reg = <0x68>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		x-powers,drive-vbus-en;
	};
};

#include "axp22x.dtsi"

&ac_power_supply {
	status = "okay";
};

&reg_aldo1 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <2700000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "avcc";
};

&reg_dc5ldo {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-cpus";
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "vcc-3v0";
};

&reg_dcdc2 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-gpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc4 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-sys-dll";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

&reg_drivevbus {
	regulator-name = "usb0-vbus";
	status = "okay";
};

&reg_usb1_vbus {
	gpio = <&pio 7 24 GPIO_ACTIVE_HIGH>; /* PH24 */
	status = "okay";
};

&tcon0 {
	pinctrl-names = "default";
	pinctrl-0 = <&lcd0_rgb888_pins>;
};

&tcon0_out {
	tcon0_out_vga: endpoint@0 {
		reg = <0>;
		remote-endpoint = <&vga_dac_in>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_ph_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 0 15 GPIO_ACTIVE_HIGH>; /* PA15 */
	usb0_vbus_det-gpios = <&pio 0 16 GPIO_ACTIVE_HIGH>; /* PA16 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_drivevbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};
