/*
 * Copyright 2014 <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun6i-a31.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Mele A1000G Quad top set box";
	compatible = "mele,a1000g-quad", "allwinner,sun6i-a31";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led {
			label = "a1000g:blue:pwr";
			gpios = <&pio 7 13 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc3>;
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_mii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "mii";
	phy-supply = <&reg_dldo1>;
	status = "okay";
};

&ir {
	pinctrl-names = "default";
	pinctrl-0 = <&s_ir_rx_pin>;
	status = "okay";
};

&mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 7 22 GPIO_ACTIVE_LOW>; /* PH22 */
	status = "okay";
};

&p2wi {
	status = "okay";

	axp22x: pmic@68 {
		compatible = "x-powers,axp221";
		reg = <0x68>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
	};
};

#include "axp22x.dtsi"

&reg_aldo1 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <2700000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "avcc";
};

&reg_dc5ldo {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-cpus"; /* This is an educated guess */
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-3v3";
};

&reg_dcdc2 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-gpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc4 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-sys-dll";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

&reg_dldo1 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-ethernet-phy";
};

/*
 * Both reg_usb1_vbus and reg_dldo4 need to be on for the hub attached
 * to usb1 to work, and we can list only one usb1_vbus-supply, so dldo4 is
 * marked as regulator-always-on.
 */
&reg_dldo4 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-usb-hub";
};

&reg_usb1_vbus {
	gpio = <&pio 2 27 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_ph_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "host";
	status = "okay";
};

&usbphy {
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_aldo1>;
	status = "okay";
};
