/*
 * Copyright 2013 <PERSON><PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/thermal/thermal.h>

#include <dt-bindings/clock/sun6i-a31-ccu.h>
#include <dt-bindings/clock/sun6i-rtc.h>
#include <dt-bindings/reset/sun6i-a31-ccu.h>

/ {
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		ethernet0 = &gmac;
	};

	chosen {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		simplefb_hdmi: framebuffer-lcd0-hdmi {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_be0-lcd0-hdmi";
			clocks = <&ccu CLK_AHB1_BE0>, <&ccu CLK_AHB1_LCD0>,
				 <&ccu CLK_AHB1_HDMI>, <&ccu CLK_DRAM_BE0>,
				 <&ccu CLK_IEP_DRC0>, <&ccu CLK_BE0>,
				 <&ccu CLK_LCD0_CH1>, <&ccu CLK_HDMI>;
			status = "disabled";
		};

		simplefb_lcd: framebuffer-lcd0 {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_be0-lcd0";
			clocks = <&ccu CLK_AHB1_BE0>, <&ccu CLK_AHB1_LCD0>,
				 <&ccu CLK_DRAM_BE0>, <&ccu CLK_IEP_DRC0>,
				 <&ccu CLK_BE0>, <&ccu CLK_LCD0_CH0>;
			status = "disabled";
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		clock-frequency = <24000000>;
		arm,cpu-registers-not-fw-configured;
	};

	cpus {
		enable-method = "allwinner,sun6i-a31";
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clocks = <&ccu CLK_CPU>;
			clock-latency = <244144>; /* 8 32k periods */
			operating-points =
				/* kHz	  uV */
				<1008000 1200000>,
				<864000 1200000>,
				<720000 1100000>,
				<480000 1000000>;
			#cooling-cells = <2>;
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <1>;
			clocks = <&ccu CLK_CPU>;
			clock-latency = <244144>; /* 8 32k periods */
			operating-points =
				/* kHz	  uV */
				<1008000 1200000>,
				<864000 1200000>,
				<720000 1100000>,
				<480000 1000000>;
			#cooling-cells = <2>;
		};

		cpu2: cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <2>;
			clocks = <&ccu CLK_CPU>;
			clock-latency = <244144>; /* 8 32k periods */
			operating-points =
				/* kHz	  uV */
				<1008000 1200000>,
				<864000 1200000>,
				<720000 1100000>,
				<480000 1000000>;
			#cooling-cells = <2>;
		};

		cpu3: cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <3>;
			clocks = <&ccu CLK_CPU>;
			clock-latency = <244144>; /* 8 32k periods */
			operating-points =
				/* kHz	  uV */
				<1008000 1200000>,
				<864000 1200000>,
				<720000 1100000>,
				<480000 1000000>;
			#cooling-cells = <2>;
		};
	};

	thermal-zones {
		cpu-thermal {
			/* milliseconds */
			polling-delay-passive = <250>;
			polling-delay = <1000>;
			thermal-sensors = <&rtp>;

			cooling-maps {
				map0 {
					trip = <&cpu_alert0>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};

			trips {
				cpu_alert0: cpu_alert0 {
					/* milliCelsius */
					temperature = <70000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_crit: cpu_crit {
					/* milliCelsius */
					temperature = <100000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: clk-24M {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-accuracy = <50000>;
			clock-output-names = "osc24M";
		};

		osc32k: clk-32k {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-accuracy = <50000>;
			clock-output-names = "ext_osc32k";
		};

		/*
		 * The following two are dummy clocks, placeholders
		 * used in the gmac_tx clock. The gmac driver will
		 * choose one parent depending on the PHY interface
		 * mode, using clk_set_rate auto-reparenting.
		 *
		 * The actual TX clock rate is not controlled by the
		 * gmac_tx clock.
		 */
		mii_phy_tx_clk: clk-mii-phy-tx {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <25000000>;
			clock-output-names = "mii_phy_tx";
		};

		gmac_int_tx_clk: clk-gmac-int-tx {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <125000000>;
			clock-output-names = "gmac_int_tx";
		};

		gmac_tx_clk: clk@1c200d0 {
			#clock-cells = <0>;
			compatible = "allwinner,sun7i-a20-gmac-clk";
			reg = <0x01c200d0 0x4>;
			clocks = <&mii_phy_tx_clk>, <&gmac_int_tx_clk>;
			clock-output-names = "gmac_tx";
		};
	};

	de: display-engine {
		compatible = "allwinner,sun6i-a31-display-engine";
		allwinner,pipelines = <&fe0>, <&fe1>;
		status = "disabled";
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun6i-a31-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_DMA>;
			resets = <&ccu RST_AHB1_DMA>;
			#dma-cells = <1>;
		};

		tcon0: lcd-controller@1c0c000 {
			compatible = "allwinner,sun6i-a31-tcon";
			reg = <0x01c0c000 0x1000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dma 11>;
			resets = <&ccu RST_AHB1_LCD0>,
				 <&ccu RST_AHB1_LVDS>;
			reset-names = "lcd",
				      "lvds";
			clocks = <&ccu CLK_AHB1_LCD0>,
				 <&ccu CLK_LCD0_CH0>,
				 <&ccu CLK_LCD0_CH1>,
				 <&ccu 15>;
			clock-names = "ahb",
				      "tcon-ch0",
				      "tcon-ch1",
				      "lvds-alt";
			clock-output-names = "tcon0-pixel-clock";
			#clock-cells = <0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon0_in_drc0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&drc0_out_tcon0>;
					};

					tcon0_in_drc1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&drc1_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon0_out_hdmi: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&hdmi_in_tcon0>;
						allwinner,tcon-channel = <1>;
					};
				};
			};
		};

		tcon1: lcd-controller@1c0d000 {
			compatible = "allwinner,sun6i-a31-tcon";
			reg = <0x01c0d000 0x1000>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dma 12>;
			resets = <&ccu RST_AHB1_LCD1>,
				 <&ccu RST_AHB1_LVDS>;
			reset-names = "lcd", "lvds";
			clocks = <&ccu CLK_AHB1_LCD1>,
				 <&ccu CLK_LCD1_CH0>,
				 <&ccu CLK_LCD1_CH1>,
				 <&ccu 15>;
			clock-names = "ahb",
				      "tcon-ch0",
				      "tcon-ch1",
				      "lvds-alt";
			clock-output-names = "tcon1-pixel-clock";
			#clock-cells = <0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon1_in_drc0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&drc0_out_tcon1>;
					};

					tcon1_in_drc1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&drc1_out_tcon1>;
					};
				};

				tcon1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon1_out_hdmi: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&hdmi_in_tcon1>;
						allwinner,tcon-channel = <1>;
					};
				};
			};
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_AHB1_MMC0>,
				 <&ccu CLK_MMC0>,
				 <&ccu CLK_MMC0_OUTPUT>,
				 <&ccu CLK_MMC0_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_AHB1_MMC0>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_AHB1_MMC1>,
				 <&ccu CLK_MMC1>,
				 <&ccu CLK_MMC1_OUTPUT>,
				 <&ccu CLK_MMC1_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_AHB1_MMC1>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&ccu CLK_AHB1_MMC2>,
				 <&ccu CLK_MMC2>,
				 <&ccu CLK_MMC2_OUTPUT>,
				 <&ccu CLK_MMC2_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_AHB1_MMC2>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc3: mmc@1c12000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c12000 0x1000>;
			clocks = <&ccu CLK_AHB1_MMC3>,
				 <&ccu CLK_MMC3>,
				 <&ccu CLK_MMC3_OUTPUT>,
				 <&ccu CLK_MMC3_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_AHB1_MMC3>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 63 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		hdmi: hdmi@1c16000 {
			compatible = "allwinner,sun6i-a31-hdmi";
			reg = <0x01c16000 0x1000>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_HDMI>, <&ccu CLK_HDMI>,
				 <&ccu CLK_HDMI_DDC>,
				 <&ccu CLK_PLL_VIDEO0_2X>,
				 <&ccu CLK_PLL_VIDEO1_2X>;
			clock-names = "ahb", "mod", "ddc", "pll-0", "pll-1";
			resets = <&ccu RST_AHB1_HDMI>;
			dma-names = "ddc-tx", "ddc-rx", "audio-tx";
			dmas = <&dma 13>, <&dma 13>, <&dma 14>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					hdmi_in_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_out_hdmi>;
					};

					hdmi_in_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_out_hdmi>;
					};
				};

				hdmi_out: port@1 {
					reg = <1>;
				};
			};
		};

		usb_otg: usb@1c19000 {
			compatible = "allwinner,sun6i-a31-musb";
			reg = <0x01c19000 0x0400>;
			clocks = <&ccu CLK_AHB1_OTG>;
			resets = <&ccu RST_AHB1_OTG>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			dr_mode = "otg";
			status = "disabled";
		};

		usbphy: phy@1c19400 {
			compatible = "allwinner,sun6i-a31-usb-phy";
			reg = <0x01c19400 0x10>,
			      <0x01c1a800 0x4>,
			      <0x01c1b800 0x4>;
			reg-names = "phy_ctrl",
				    "pmu1",
				    "pmu2";
			clocks = <&ccu CLK_USB_PHY0>,
				 <&ccu CLK_USB_PHY1>,
				 <&ccu CLK_USB_PHY2>;
			clock-names = "usb0_phy",
				      "usb1_phy",
				      "usb2_phy";
			resets = <&ccu RST_USB_PHY0>,
				 <&ccu RST_USB_PHY1>,
				 <&ccu RST_USB_PHY2>;
			reset-names = "usb0_reset",
				      "usb1_reset",
				      "usb2_reset";
			status = "disabled";
			#phy-cells = <1>;
		};

		ehci0: usb@1c1a000 {
			compatible = "allwinner,sun6i-a31-ehci", "generic-ehci";
			reg = <0x01c1a000 0x100>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_EHCI0>;
			resets = <&ccu RST_AHB1_EHCI0>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci0: usb@1c1a400 {
			compatible = "allwinner,sun6i-a31-ohci", "generic-ohci";
			reg = <0x01c1a400 0x100>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_OHCI0>, <&ccu CLK_USB_OHCI0>;
			resets = <&ccu RST_AHB1_OHCI0>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ehci1: usb@1c1b000 {
			compatible = "allwinner,sun6i-a31-ehci", "generic-ehci";
			reg = <0x01c1b000 0x100>;
			interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_EHCI1>;
			resets = <&ccu RST_AHB1_EHCI1>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci1: usb@1c1b400 {
			compatible = "allwinner,sun6i-a31-ohci", "generic-ohci";
			reg = <0x01c1b400 0x100>;
			interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_OHCI1>, <&ccu CLK_USB_OHCI1>;
			resets = <&ccu RST_AHB1_OHCI1>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci2: usb@1c1c400 {
			compatible = "allwinner,sun6i-a31-ohci", "generic-ohci";
			reg = <0x01c1c400 0x100>;
			interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_OHCI2>, <&ccu CLK_USB_OHCI2>;
			resets = <&ccu RST_AHB1_OHCI2>;
			status = "disabled";
		};

		ccu: clock@1c20000 {
			compatible = "allwinner,sun6i-a31-ccu";
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&rtc CLK_OSC32K>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			compatible = "allwinner,sun6i-a31-pinctrl";
			reg = <0x01c20800 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB1_PIO>, <&osc24M>,
				 <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			gmac_gmii_pins: gmac-gmii-pins {
				pins = "PA0", "PA1", "PA2", "PA3",
						"PA4", "PA5", "PA6", "PA7",
						"PA8", "PA9", "PA10", "PA11",
						"PA12", "PA13", "PA14",	"PA15",
						"PA16", "PA17", "PA18", "PA19",
						"PA20", "PA21", "PA22", "PA23",
						"PA24", "PA25", "PA26", "PA27";
				function = "gmac";
				/*
				 * data lines in GMII mode run at 125MHz and
				 * might need a higher signal drive strength
				 */
				drive-strength = <30>;
			};

			gmac_mii_pins: gmac-mii-pins {
				pins = "PA0", "PA1", "PA2", "PA3",
						"PA8", "PA9", "PA11",
						"PA12", "PA13", "PA14", "PA19",
						"PA20", "PA21", "PA22", "PA23",
						"PA24", "PA26", "PA27";
				function = "gmac";
			};

			gmac_rgmii_pins: gmac-rgmii-pins {
				pins = "PA0", "PA1", "PA2", "PA3",
						"PA9", "PA10", "PA11",
						"PA12", "PA13", "PA14", "PA19",
						"PA20", "PA25", "PA26", "PA27";
				function = "gmac";
				/*
				 * data lines in RGMII mode use DDR mode
				 * and need a higher signal drive strength
				 */
				drive-strength = <40>;
			};

			i2c0_pins: i2c0-pins {
				pins = "PH14", "PH15";
				function = "i2c0";
			};

			i2c1_pins: i2c1-pins {
				pins = "PH16", "PH17";
				function = "i2c1";
			};

			i2c2_pins: i2c2-pins {
				pins = "PH18", "PH19";
				function = "i2c2";
			};

			lcd0_rgb888_pins: lcd0-rgb888-pins {
				pins = "PD0", "PD1", "PD2", "PD3",
						 "PD4", "PD5", "PD6", "PD7",
						 "PD8", "PD9", "PD10", "PD11",
						 "PD12", "PD13", "PD14", "PD15",
						 "PD16", "PD17", "PD18", "PD19",
						 "PD20", "PD21", "PD22", "PD23",
						 "PD24", "PD25", "PD26", "PD27";
				function = "lcd0";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2",
						 "PF3", "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc1_pins: mmc1-pins {
				pins = "PG0", "PG1", "PG2", "PG3",
						 "PG4", "PG5";
				function = "mmc1";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_4bit_pins: mmc2-4bit-pins {
				pins = "PC6", "PC7", "PC8", "PC9",
						 "PC10", "PC11";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_8bit_emmc_pins: mmc2-8bit-emmc-pins {
				pins = "PC6", "PC7", "PC8", "PC9",
						 "PC10", "PC11", "PC12",
						 "PC13", "PC14", "PC15",
						 "PC24";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc3_8bit_emmc_pins: mmc3-8bit-emmc-pins {
				pins = "PC6", "PC7", "PC8", "PC9",
						 "PC10", "PC11", "PC12",
						 "PC13", "PC14", "PC15",
						 "PC24";
				function = "mmc3";
				drive-strength = <40>;
				bias-pull-up;
			};

			spdif_tx_pin: spdif-tx-pin {
				pins = "PH28";
				function = "spdif";
			};

			uart0_ph_pins: uart0-ph-pins {
				pins = "PH20", "PH21";
				function = "uart0";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun4i-a10-timer";
			reg = <0x01c20c00 0xa0>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		wdt1: watchdog@1c20ca0 {
			compatible = "allwinner,sun6i-a31-wdt";
			reg = <0x01c20ca0 0x20>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		spdif: spdif@1c21000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun6i-a31-spdif";
			reg = <0x01c21000 0x400>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB1_SPDIF>, <&ccu CLK_SPDIF>;
			resets = <&ccu RST_APB1_SPDIF>;
			clock-names = "apb", "spdif";
			dmas = <&dma 2>, <&dma 2>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s0: i2s@1c22000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun6i-a31-i2s";
			reg = <0x01c22000 0x400>;
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB1_DAUDIO0>, <&ccu CLK_DAUDIO0>;
			resets = <&ccu RST_APB1_DAUDIO0>;
			clock-names = "apb", "mod";
			dmas = <&dma 3>, <&dma 3>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s1: i2s@1c22400 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun6i-a31-i2s";
			reg = <0x01c22400 0x400>;
			interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB1_DAUDIO1>, <&ccu CLK_DAUDIO1>;
			resets = <&ccu RST_APB1_DAUDIO1>;
			clock-names = "apb", "mod";
			dmas = <&dma 4>, <&dma 4>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		lradc: lradc@1c22800 {
			compatible = "allwinner,sun4i-a10-lradc-keys";
			reg = <0x01c22800 0x100>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		rtp: rtp@1c25000 {
			compatible = "allwinner,sun6i-a31-ts";
			reg = <0x01c25000 0x100>;
			interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
			#thermal-sensor-cells = <0>;
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB2_UART0>;
			resets = <&ccu RST_APB2_UART0>;
			dmas = <&dma 6>, <&dma 6>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB2_UART1>;
			resets = <&ccu RST_APB2_UART1>;
			dmas = <&dma 7>, <&dma 7>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB2_UART2>;
			resets = <&ccu RST_APB2_UART2>;
			dmas = <&dma 8>, <&dma 8>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart3: serial@1c28c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28c00 0x400>;
			interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB2_UART3>;
			resets = <&ccu RST_APB2_UART3>;
			dmas = <&dma 9>, <&dma 9>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart4: serial@1c29000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29000 0x400>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB2_UART4>;
			resets = <&ccu RST_APB2_UART4>;
			dmas = <&dma 10>, <&dma 10>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart5: serial@1c29400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29400 0x400>;
			interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_APB2_UART5>;
			resets = <&ccu RST_APB2_UART5>;
			dmas = <&dma 22>, <&dma 22>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB2_I2C0>;
			resets = <&ccu RST_APB2_I2C0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB2_I2C1>;
			resets = <&ccu RST_APB2_I2C1>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@1c2b400 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b400 0x400>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB2_I2C2>;
			resets = <&ccu RST_APB2_I2C2>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c3: i2c@1c2b800 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b800 0x400>;
			interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB2_I2C3>;
			resets = <&ccu RST_APB2_I2C3>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		gmac: ethernet@1c30000 {
			compatible = "allwinner,sun7i-a20-gmac";
			reg = <0x01c30000 0x1054>;
			interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq";
			clocks = <&ccu CLK_AHB1_EMAC>, <&gmac_tx_clk>;
			clock-names = "stmmaceth", "allwinner_gmac_tx";
			resets = <&ccu RST_AHB1_EMAC>;
			reset-names = "stmmaceth";
			snps,pbl = <2>;
			snps,fixed-burst;
			snps,force_sf_dma_mode;
			status = "disabled";

			mdio: mdio {
				compatible = "snps,dwmac-mdio";
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};

		crypto: crypto-engine@1c15000 {
			compatible = "allwinner,sun6i-a31-crypto",
				     "allwinner,sun4i-a10-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_SS>, <&ccu CLK_SS>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_AHB1_SS>;
			reset-names = "ahb";
		};

		codec: codec@1c22c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun6i-a31-codec";
			reg = <0x01c22c00 0x400>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_APB1_CODEC>, <&ccu CLK_CODEC>;
			clock-names = "apb", "codec";
			resets = <&ccu RST_APB1_CODEC>;
			dmas = <&dma 15>, <&dma 15>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		timer@1c60000 {
			compatible = "allwinner,sun6i-a31-hstimer",
				     "allwinner,sun7i-a20-hstimer";
			reg = <0x01c60000 0x1000>;
			interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_HSTIMER>;
			resets = <&ccu RST_AHB1_HSTIMER>;
		};

		spi0: spi@1c68000 {
			compatible = "allwinner,sun6i-a31-spi";
			reg = <0x01c68000 0x1000>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_SPI0>, <&ccu CLK_SPI0>;
			clock-names = "ahb", "mod";
			dmas = <&dma 23>, <&dma 23>;
			dma-names = "rx", "tx";
			resets = <&ccu RST_AHB1_SPI0>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi1: spi@1c69000 {
			compatible = "allwinner,sun6i-a31-spi";
			reg = <0x01c69000 0x1000>;
			interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_SPI1>, <&ccu CLK_SPI1>;
			clock-names = "ahb", "mod";
			dmas = <&dma 24>, <&dma 24>;
			dma-names = "rx", "tx";
			resets = <&ccu RST_AHB1_SPI1>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi2: spi@1c6a000 {
			compatible = "allwinner,sun6i-a31-spi";
			reg = <0x01c6a000 0x1000>;
			interrupts = <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_SPI2>, <&ccu CLK_SPI2>;
			clock-names = "ahb", "mod";
			dmas = <&dma 25>, <&dma 25>;
			dma-names = "rx", "tx";
			resets = <&ccu RST_AHB1_SPI2>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi3: spi@1c6b000 {
			compatible = "allwinner,sun6i-a31-spi";
			reg = <0x01c6b000 0x1000>;
			interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_SPI3>, <&ccu CLK_SPI3>;
			clock-names = "ahb", "mod";
			dmas = <&dma 26>, <&dma 26>;
			dma-names = "rx", "tx";
			resets = <&ccu RST_AHB1_SPI3>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		gic: interrupt-controller@1c81000 {
			compatible = "arm,gic-400";
			reg = <0x01c81000 0x1000>,
			      <0x01c82000 0x2000>,
			      <0x01c84000 0x2000>,
			      <0x01c86000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		fe0: display-frontend@1e00000 {
			compatible = "allwinner,sun6i-a31-display-frontend";
			reg = <0x01e00000 0x20000>;
			interrupts = <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_FE0>, <&ccu CLK_FE0>,
				 <&ccu CLK_DRAM_FE0>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_AHB1_FE0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					fe0_out_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_in_fe0>;
					};

					fe0_out_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_in_fe0>;
					};
				};
			};
		};

		fe1: display-frontend@1e20000 {
			compatible = "allwinner,sun6i-a31-display-frontend";
			reg = <0x01e20000 0x20000>;
			interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_FE1>, <&ccu CLK_FE1>,
				 <&ccu CLK_DRAM_FE1>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_AHB1_FE1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					fe1_out_be0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&be0_in_fe1>;
					};

					fe1_out_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_in_fe1>;
					};
				};
			};
		};

		be1: display-backend@1e40000 {
			compatible = "allwinner,sun6i-a31-display-backend";
			reg = <0x01e40000 0x10000>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_BE1>, <&ccu CLK_BE1>,
				 <&ccu CLK_DRAM_BE1>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_AHB1_BE1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					be1_in_fe0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&fe0_out_be1>;
					};

					be1_in_fe1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&fe1_out_be1>;
					};
				};

				be1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					be1_out_drc1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&drc1_in_be1>;
					};
				};
			};
		};

		drc1: drc@1e50000 {
			compatible = "allwinner,sun6i-a31-drc";
			reg = <0x01e50000 0x10000>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_DRC1>, <&ccu CLK_IEP_DRC1>,
				 <&ccu CLK_DRAM_DRC1>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_AHB1_DRC1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				drc1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					drc1_in_be1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&be1_out_drc1>;
					};
				};

				drc1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					drc1_out_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_in_drc1>;
					};

					drc1_out_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_in_drc1>;
					};
				};
			};
		};

		be0: display-backend@1e60000 {
			compatible = "allwinner,sun6i-a31-display-backend";
			reg = <0x01e60000 0x10000>;
			interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_BE0>, <&ccu CLK_BE0>,
				 <&ccu CLK_DRAM_BE0>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_AHB1_BE0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be0_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					be0_in_fe0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&fe0_out_be0>;
					};

					be0_in_fe1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&fe1_out_be0>;
					};
				};

				be0_out: port@1 {
					reg = <1>;

					be0_out_drc0: endpoint {
						remote-endpoint = <&drc0_in_be0>;
					};
				};
			};
		};

		drc0: drc@1e70000 {
			compatible = "allwinner,sun6i-a31-drc";
			reg = <0x01e70000 0x10000>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_AHB1_DRC0>, <&ccu CLK_IEP_DRC0>,
				 <&ccu CLK_DRAM_DRC0>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_AHB1_DRC0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				drc0_in: port@0 {
					reg = <0>;

					drc0_in_be0: endpoint {
						remote-endpoint = <&be0_out_drc0>;
					};
				};

				drc0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					drc0_out_tcon0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon0_in_drc0>;
					};

					drc0_out_tcon1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon1_in_drc0>;
					};
				};
			};
		};

		rtc: rtc@1f00000 {
			#clock-cells = <1>;
			compatible = "allwinner,sun6i-a31-rtc";
			reg = <0x01f00000 0x54>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc32k>;
			clock-output-names = "osc32k";
		};

		r_intc: interrupt-controller@1f00c00 {
			compatible = "allwinner,sun6i-a31-r-intc";
			interrupt-controller;
			#interrupt-cells = <3>;
			reg = <0x01f00c00 0x400>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		};

		prcm@1f01400 {
			compatible = "allwinner,sun6i-a31-prcm";
			reg = <0x01f01400 0x200>;

			ar100: ar100_clk {
				compatible = "allwinner,sun6i-a31-ar100-clk";
				#clock-cells = <0>;
				clocks = <&rtc CLK_OSC32K>, <&osc24M>,
					 <&ccu CLK_PLL_PERIPH>,
					 <&ccu CLK_PLL_PERIPH>;
				clock-output-names = "ar100";
			};

			ahb0: ahb0_clk {
				compatible = "fixed-factor-clock";
				#clock-cells = <0>;
				clock-div = <1>;
				clock-mult = <1>;
				clocks = <&ar100>;
				clock-output-names = "ahb0";
			};

			apb0: apb0_clk {
				compatible = "allwinner,sun6i-a31-apb0-clk";
				#clock-cells = <0>;
				clocks = <&ahb0>;
				clock-output-names = "apb0";
			};

			apb0_gates: apb0_gates_clk {
				compatible = "allwinner,sun6i-a31-apb0-gates-clk";
				#clock-cells = <1>;
				clocks = <&apb0>;
				clock-output-names = "apb0_pio", "apb0_ir",
						"apb0_timer", "apb0_p2wi",
						"apb0_uart", "apb0_1wire",
						"apb0_i2c";
			};

			ir_clk: ir_clk {
				#clock-cells = <0>;
				compatible = "allwinner,sun4i-a10-mod0-clk";
				clocks = <&rtc CLK_OSC32K>, <&osc24M>;
				clock-output-names = "ir";
			};

			apb0_rst: apb0_rst {
				compatible = "allwinner,sun6i-a31-clock-reset";
				#reset-cells = <1>;
			};
		};

		cpucfg@1f01c00 {
			compatible = "allwinner,sun6i-a31-cpuconfig";
			reg = <0x01f01c00 0x300>;
		};

		ir: ir@1f02000 {
			compatible = "allwinner,sun6i-a31-ir";
			clocks = <&apb0_gates 1>, <&ir_clk>;
			clock-names = "apb", "ir";
			resets = <&apb0_rst 1>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			reg = <0x01f02000 0x40>;
			status = "disabled";
		};

		r_pio: pinctrl@1f02c00 {
			compatible = "allwinner,sun6i-a31-r-pinctrl";
			reg = <0x01f02c00 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&apb0_gates 0>, <&osc24M>, <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			s_ir_rx_pin: s-ir-rx-pin {
				pins = "PL4";
				function = "s_ir";
			};

			s_p2wi_pins: s-p2wi-pins {
				pins = "PL0", "PL1";
				function = "s_p2wi";
			};
		};

		p2wi: i2c@1f03400 {
			compatible = "allwinner,sun6i-a31-p2wi";
			reg = <0x01f03400 0x400>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&apb0_gates 3>;
			clock-frequency = <100000>;
			resets = <&apb0_rst 3>;
			pinctrl-names = "default";
			pinctrl-0 = <&s_p2wi_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};
};
