/*
 * Copyright 2014 <PERSON>ar<PERSON><PERSON> <<EMAIL>>
 * Copyright 2015 <PERSON><PERSON> <<EMAIL>>
 * Copyright 2015 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun6i-a31s.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "MSI Primo81 tablet";
	compatible = "msi,primo81", "allwinner,sun6i-a31s";

	hdmi-connector {
		compatible = "hdmi-connector";
		type = "c";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc3>;
};

&de {
	status = "okay";
};

&ehci0 {
	/* rtl8188etv wifi is connected here */
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c0 {
	/* pull-ups and device VDDIO use AXP221 DLDO3 */
	status = "failed";
};

&i2c1 {
	status = "okay";

	ctp@5d {
		compatible = "goodix,gt911";
		reg = <0x5d>;
		interrupt-parent = <&pio>;
		interrupts = <0 3 IRQ_TYPE_LEVEL_HIGH>; /* PA3 */
		touchscreen-swapped-x-y;
	};
};

&i2c2 {
	status = "okay";

	accelerometer@1c {
		pinctrl-names = "default";
		pinctrl-0 = <&mma8452_int_primo81>;
		compatible = "fsl,mma8452";
		reg = <0x1c>;
		interrupt-parent = <&pio>;
		interrupts = <0 9 IRQ_TYPE_LEVEL_HIGH>; /* PA9 */
	};
};

&lradc {
	vref-supply = <&reg_aldo3>;
	status = "okay";

	button-158 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <158730>;
	};

	button-349 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <349206>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 0 8 GPIO_ACTIVE_LOW>; /* PA8 */
	status = "okay";
};

&pio {
	mma8452_int_primo81: mma8452-int-pin {
		pins = "PA9";
		function = "gpio_in";
		bias-pull-up;
	};
};

&p2wi {
	status = "okay";

	axp22x: pmic@68 {
		compatible = "x-powers,axp221";
		reg = <0x68>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		x-powers,drive-vbus-en;
	};
};

#include "axp22x.dtsi"

&battery_power_supply {
	status = "okay";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <2700000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "avcc";
};

&reg_dc1sw {
	regulator-name = "vcc-lcd";
};

&reg_dc5ldo {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-cpus"; /* This is an educated guess */
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "vcc-3v0";
};

&reg_dcdc2 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-gpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc4 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-sys-dll";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

&reg_dldo1 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi";
};

&reg_dldo3 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-name = "vddio-csi";
};

&reg_drivevbus {
	regulator-name = "usb0-vbus";
	status = "okay";
};

&reg_eldo3 {
	regulator-min-microvolt = <1080000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd-mipi-bridge";
};

&simplefb_lcd {
	vcc-lcd-supply = <&reg_dc1sw>;
	vdd-mipi-bridge-supply = <&reg_eldo3>;
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 0 15 GPIO_ACTIVE_HIGH>; /* PA15 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_drivevbus>;
	usb1_vbus-supply = <&reg_dldo1>;
	status = "okay";
};
