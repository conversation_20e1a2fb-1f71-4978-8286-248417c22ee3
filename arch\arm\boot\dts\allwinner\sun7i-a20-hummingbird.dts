/*
 * Copyright 2013 Wills Wang
 *
 * <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun7i-a20.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "Merrii A20 Hummingbird";
	compatible = "merrii,a20-hummingbird", "allwinner,sun7i-a20";

	aliases {
		serial0 = &uart0;
		serial1 = &uart2;
		serial2 = &uart3;
		serial3 = &uart4;
		serial4 = &uart5;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	reg_mmc3_vdd: mmc3_vdd {
		compatible = "regulator-fixed";
		regulator-name = "mmc3_vdd";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		enable-active-high;
		gpio = <&pio 7 9 GPIO_ACTIVE_HIGH>; /* PH9 */
	};

	reg_gmac_vdd: gmac_vdd {
		compatible = "regulator-fixed";
		regulator-name = "gmac_vdd";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
		enable-active-high;
		gpio = <&pio 7 16 GPIO_ACTIVE_HIGH>; /* PH16 */
	};
};

&ahci {
	target-supply = <&reg_ahci_5v>;
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "rgmii";
	phy-supply = <&reg_gmac_vdd>;
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		compatible = "x-powers,axp209";
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
		interrupt-controller;
		#interrupt-cells = <1>;
	};
};

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";
};

&i2c3 {
	status = "okay";
};

&ir0 {
	pinctrl-names = "default";
	pinctrl-0 = <&ir0_rx_pin>;
	status = "okay";
};

&gmac_mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
		reset-gpios = <&pio 0 17 GPIO_ACTIVE_LOW>; /* PA17 */
		reset-assert-us = <10000>;
		/* wait 1s after reset, otherwise fail to read phy id */
		reset-deassert-us = <1000000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v0>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&mmc3 {
	vmmc-supply = <&reg_mmc3_vdd>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pin>;
	status = "okay";
};

&reg_ahci_5v {
	gpio = <&pio 7 15 GPIO_ACTIVE_HIGH>; /* PH15 */
	status = "okay";
};

&reg_usb1_vbus {
	gpio = <&pio 7 2 GPIO_ACTIVE_HIGH>; /* PH2 */
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pb_pins>,
		    <&spi2_cs0_pb_pin>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pi_pins>, <&uart2_cts_rts_pi_pins>;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pg_pins>, <&uart3_cts_rts_pg_pins>;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pg_pins>;
	status = "okay";
};

&uart5 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart5_pi_pins>;
	status = "okay";
};

&usbphy {
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
