// SPDX-License-Identifier: (GPL-2.0 OR MIT)
// Copyright (C) 2023 In-Circuit GmbH

/dts-v1/;

#include "sun7i-a20-icnova-a20.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>

/ {
	model = "In-Circuit ICnova A20 ADB4006";
	compatible = "incircuit,icnova-a20-adb4006", "incircuit,icnova-a20",
		     "allwinner,sun7i-a20";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	hdmi-connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			function = LED_FUNCTION_POWER;
			color = <LED_COLOR_ID_YELLOW>;
			gpios = <&pio 7 21 GPIO_ACTIVE_HIGH>; /* PH21 */
			default-state = "on";
		};

		led-1 {
			function = LED_FUNCTION_HEARTBEAT;
			color = <LED_COLOR_ID_RED>;
			gpios = <&pio 7 20 GPIO_ACTIVE_HIGH>; /* PH20 */
			linux,default-trigger = "heartbeat";
		};
	};
};

&ahci {
	target-supply = <&reg_ahci_5v>;
	status = "okay";
};

&codec {
	status = "okay";
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&reg_ahci_5v {
	status = "okay";
};

&ac_power_supply {
	status = "okay";
};

&reg_usb1_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 4 GPIO_ACTIVE_HIGH>; /* PH4 */
	usb0_vbus_det-gpios = <&pio 7 5 GPIO_ACTIVE_HIGH>; /* PH5 */
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
