/*
 * Copyright 2015 - <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun7i-a20.dtsi"
#include "sunxi-itead-core-common.dtsi"

/ {
	model = "Itead Ibox A20";
	compatible = "itead,itead-ibox-a20", "allwinner,sun7i-a20";

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&led_pins_itead_core>;

		led-0 {
			label = "itead_core:green:usr";
			gpios = <&pio 7 20 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};

		led-1 {
			label = "itead_core:blue:usr";
			gpios = <&pio 7 21 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "On-board SPDIF";

		simple-audio-card,cpu {
			sound-dai = <&spdif>;
		};

		simple-audio-card,codec {
			sound-dai = <&spdif_out>;
		};
	};

	spdif_out: spdif-out {
		#sound-dai-cells = <0>;
		compatible = "linux,spdif-dit";
	};
};

&ahci {
	target-supply = <&reg_ahci_5v>;
	status = "okay";
};

&codec {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_mii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "mii";
	status = "okay";
};

&gmac_mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&i2c0 {
	axp209: pmic@34 {
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

&ir0 {
	pinctrl-names = "default";
	pinctrl-0 = <&ir0_rx_pin>;
	status = "okay";
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&pio {
	led_pins_itead_core: led-pins {
		pins = "PH20","PH21";
		function = "gpio_out";
		drive-strength = <20>;
	};
};

&reg_ahci_5v {
	status = "okay";
};

&spdif {
	pinctrl-names = "default";
	pinctrl-0 = <&spdif_tx_pin>;
	status = "okay";
};
