// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2020 Linutronix GmbH
 * Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun7i-a20-lamobo-r1.dts"

/ {
	model = "Lamobo R1";
	compatible = "linutronix,testbox-v2", "lamobo,lamobo-r1", "allwinner,sun7i-a20";

	leds {
		led-opto1 {
			label = "lamobo_r1:opto:powerswitch";
			gpios = <&pio 7 3 GPIO_ACTIVE_HIGH>;
		};

		led-opto2 {
			label = "lamobo_r1:opto:relay";
			gpios = <&pio 7 5 GPIO_ACTIVE_HIGH>;
		};
	};
};

&i2c2 {
	clock-frequency = <100000>;
	status = "okay";

	eeprom: eeprom@50 {
		compatible = "atmel,24c08";
		reg = <0x50>;
		status = "okay";
	};

	atecc508a@60 {
		compatible = "atmel,atecc508a";
		reg = <0x60>;
	};
};

&can0 {
	pinctrl-names = "default";
	pinctrl-0 = <&can_ph_pins>;
	status = "okay";
};
