// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Device Tree Source for A20-SOM204-EVB Board
 *
 * Copyright (C) 2018 Olimex Ltd.
 *   Author: <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun7i-a20.dtsi"
#include "sunxi-common-regulators.dtsi"


#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "Olimex A20-SOM204-EVB";
	compatible = "olimex,a20-olimex-som204-evb", "allwinner,sun7i-a20";

	aliases {
		serial0 = &uart0;
		serial1 = &uart4;
		serial2 = &uart7;
		spi0 = &spi1;
		spi1 = &spi2;
		ethernet1 = &rtl8723bs;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	hdmi-connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "a20-som204-evb:green:stat";
			gpios = <&pio 8 0 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};

		led-1 {
			label = "a20-som204-evb:green:led1";
			gpios = <&pio 8 10 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};

		led-2 {
			label = "a20-som204-evb:yellow:led2";
			gpios = <&pio 8 11 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

	rtl_pwrseq: rtl_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 6 9 GPIO_ACTIVE_LOW>;
	};
};

&ahci {
	target-supply = <&reg_ahci_5v>;
	status = "okay";
};

&can0 {
	pinctrl-names = "default";
	pinctrl-0 = <&can_ph_pins>;
	status = "okay";
};

&codec {
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy3>;
	phy-mode = "rgmii";
	phy-supply = <&reg_vcc3v3>;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

/* Exposed to UEXT1 */
&i2c1 {
	status = "okay";

	eeprom: eeprom@50 {
		compatible = "atmel,24c16";
		reg = <0x50>;
		pagesize = <16>;
	};
};

/* Exposed to UEXT2 */
&i2c2 {
	status = "okay";
};

&ir0 {
	pinctrl-names = "default";
	pinctrl-0 = <&ir0_rx_pin>;
	status = "okay";
};

&gmac_mdio {
	phy3: ethernet-phy@3 {
		reg = <3>;
		reset-gpios = <&pio 0 17 GPIO_ACTIVE_LOW>; /* PA17 */
		reset-assert-us = <10000>;
		/* wait 1s after reset, otherwise fail to read phy id */
		reset-deassert-us = <1000000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&mmc3 {
	vmmc-supply = <&reg_vcc3v3>;
	mmc-pwrseq = <&rtl_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	rtl8723bs: sdio_wifi@1 {
		reg = <1>;
	};
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pio {
	uart3_rts_pin: uart3-rts-pin {
		pins = "PG8";
		function = "uart3";
	};
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&reg_ahci_5v {
	gpio = <&pio 2 3 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-always-on;
	regulator-min-microvolt = <1300000>;
	regulator-max-microvolt = <1300000>;
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_ldo4 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-pg";
};

&reg_usb0_vbus {
	gpio = <&pio 2 17 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&reg_usb1_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

/* Exposed to UEXT1 */
&spi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_pi_pins>,
		    <&spi1_cs0_pi_pin>;
	status = "okay";
};

/* Exposed to UEXT2 */
&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pc_pins>,
		    <&spi2_cs0_pc_pin>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

/* Used for RTL8723BS bluetooth */
&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pg_pins>, <&uart3_rts_pin>;
	status = "okay";
};

/* Exposed to UEXT1 */
&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pg_pins>;
	status = "okay";
};

/* Exposed to UEXT2 */
&uart7 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart7_pi_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 4 GPIO_ACTIVE_HIGH>; /* PH4 */
	usb0_vbus_det-gpios = <&pio 7 5 GPIO_ACTIVE_HIGH>; /* PH5 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
