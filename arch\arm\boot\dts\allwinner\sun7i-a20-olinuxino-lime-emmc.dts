// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2020 Olimex Ltd.
 *   Author: <PERSON> <<EMAIL>>
 */

#include "sun7i-a20-olinuxino-lime.dts"

/ {
	model = "Olimex A20-OLinuXino-LIME-eMMC";
	compatible = "olimex,a20-olinuxino-lime-emmc", "allwinner,sun7i-a20";

	mmc2_pwrseq: pwrseq {
		compatible = "mmc-pwrseq-emmc";
		reset-gpios = <&pio 2 16 GPIO_ACTIVE_LOW>;
	};
};

&mmc2 {
	vmmc-supply = <&reg_vcc3v3>;
	vqmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	non-removable;
	mmc-pwrseq = <&mmc2_pwrseq>;
	status = "okay";

	emmc: emmc@0 {
		reg = <0>;
		compatible = "mmc-card";
		broken-hpi;
	};
};
