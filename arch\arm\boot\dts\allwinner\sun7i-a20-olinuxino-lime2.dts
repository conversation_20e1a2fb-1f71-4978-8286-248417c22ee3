/*
 * Copyright 2014 - <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun7i-a20.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "Olimex A20-OLinuXino-LIME2";
	compatible = "olimex,a20-olinuxino-lime2", "allwinner,sun7i-a20";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	hdmi-connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&led_pins_olinuxinolime>;

		led {
			label = "a20-olinuxino-lime2:green:usr";
			gpios = <&pio 7 2 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

	reg_axp_ipsout: axp_ipsout {
		compatible = "regulator-fixed";
		regulator-name = "axp-ipsout";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};
};

&ahci {
	target-supply = <&reg_ahci_5v>;
	status = "okay";
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "rgmii-id";
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

&i2c1 {
	status = "okay";

	eeprom: eeprom@50 {
		compatible = "atmel,24c16";
		reg = <0x50>;
		pagesize = <16>;
	};
};

&lradc {
	vref-supply = <&reg_vcc3v0>;
};

&gmac_mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pio {
	vcc-pa-supply = <&reg_vcc3v3>;
	vcc-pc-supply = <&reg_vcc3v3>;
	vcc-pe-supply = <&reg_ldo3>;
	vcc-pf-supply = <&reg_vcc3v3>;
	vcc-pg-supply = <&reg_ldo4>;

	led_pins_olinuxinolime: led-pins {
		pins = "PH2";
		function = "gpio_out";
		drive-strength = <20>;
	};
};

&reg_ahci_5v {
	gpio = <&pio 2 3 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-always-on;
	regulator-min-microvolt = <1300000>;
	regulator-max-microvolt = <1300000>;
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_ldo3 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-name = "vddio-csi0";
	regulator-soft-start;
	regulator-ramp-delay = <1600>;
};

&reg_ldo4 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-name = "vddio-csi1";
};

&reg_usb0_vbus {
	gpio = <&pio 2 17 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&reg_usb1_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 4 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PH4 */
	usb0_vbus_det-gpios = <&pio 7 5 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>; /* PH5 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
