/*
 * Copyright 2013 <PERSON><PERSON>
 *
 * <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun7i-a20.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>

/ {
	model = "Olimex A20-Olinuxino Micro";
	compatible = "olimex,a20-olinuxino-micro", "allwinner,sun7i-a20";

	aliases {
		serial0 = &uart0;
		serial1 = &uart6;
		serial2 = &uart7;
		spi0 = &spi1;
		spi1 = &spi2;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	hdmi-connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";
		pinctrl-names = "default";
		pinctrl-0 = <&led_pins_olinuxino>;

		led {
			label = "a20-olinuxino-micro:green:usr";
			gpios = <&pio 7 2 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};
};

&ahci {
	target-supply = <&reg_ahci_5v>;
	status = "okay";
};

&codec {
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_mii_pins>, <&gmac_txerr>;
	phy-handle = <&phy1>;
	phy-mode = "mii";
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

&i2c1 {
	status = "okay";

	eeprom: eeprom@50 {
		compatible = "atmel,24c16";
		reg = <0x50>;
		pagesize = <16>;
	};
};

&i2c2 {
	status = "okay";
};

&lradc {
	vref-supply = <&reg_vcc3v0>;
	status = "okay";

	button-191 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <191274>;
	};

	button-392 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <392644>;
	};

	button-601 {
		label = "Menu";
		linux,code = <KEY_MENU>;
		channel = <0>;
		voltage = <601151>;
	};

	button-795 {
		label = "Search";
		linux,code = <KEY_SEARCH>;
		channel = <0>;
		voltage = <795090>;
	};

	button-987 {
		label = "Home";
		linux,code = <KEY_HOMEPAGE>;
		channel = <0>;
		voltage = <987387>;
	};

	button-1184 {
		label = "Esc";
		linux,code = <KEY_ESC>;
		channel = <0>;
		voltage = <1184678>;
	};

	button-1398 {
		label = "Enter";
		linux,code = <KEY_ENTER>;
		channel = <0>;
		voltage = <1398804>;
	};
};

&gmac_mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&mmc3 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 11 GPIO_ACTIVE_LOW>; /* PH11 */
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pio {
	gmac_txerr: gmac-txerr-pin {
		pins = "PA17";
		function = "gmac";
	};

	led_pins_olinuxino: led-pins {
		pins = "PH2";
		function = "gpio_out";
		drive-strength = <20>;
	};
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_ahci_5v {
	status = "okay";
};

&reg_usb0_vbus {
	status = "okay";
};

&reg_usb1_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&spi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi1_pi_pins>,
		    <&spi1_cs0_pi_pin>;
	status = "okay";
};

&spi2 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi2_pc_pins>,
		    <&spi2_cs0_pc_pin>;
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart6 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart6_pi_pins>;
	status = "okay";
};

&uart7 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart7_pi_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 4 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PH4 */
	usb0_vbus_det-gpios = <&pio 7 5 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>; /* PH5 */
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
