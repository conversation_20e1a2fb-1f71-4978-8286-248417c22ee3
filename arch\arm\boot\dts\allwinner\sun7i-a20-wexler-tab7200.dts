/*
 * Copyright 2015 Alek<PERSON>lin
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun7i-a20.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "Wexler TAB7200";
	compatible = "wexler,tab7200", "allwinner,sun7i-a20";

	aliases {
		serial0 = &uart0;
	};

	backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm 0 50000 PWM_POLARITY_INVERTED>;
		brightness-levels = <0 10 20 30 40 50 60 70 80 90 100>;
		default-brightness-level = <8>;
		enable-gpios = <&pio 7 7 GPIO_ACTIVE_HIGH>; /* PH7 */
		power-supply = <&reg_vcc3v3>;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&codec {
	allwinner,pa-gpios = <&pio 7 15 GPIO_ACTIVE_HIGH>; /* PH15 */
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

#include "axp209.dtsi"

&i2c1 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	gt911: touchscreen@5d {
		compatible = "goodix,gt911";
		reg = <0x5d>;
		interrupt-parent = <&pio>;
		interrupts = <7 21 IRQ_TYPE_EDGE_FALLING>; /* EINT21 (PH21) */
		irq-gpios = <&pio 7 21 GPIO_ACTIVE_HIGH>; /* INT (PH21) */
		reset-gpios = <&pio 1 13 GPIO_ACTIVE_HIGH>; /* RST (PB13) */
		touchscreen-swapped-x-y;
	};
};

&lradc {
	vref-supply = <&reg_vcc3v0>;
	status = "okay";

	button-571 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <571428>;
	};

	button-761 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <761904>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>; /* PH1 */
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pin>;
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1450000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-int-dll";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_usb0_vbus {
	status = "okay";
};

&reg_usb1_vbus {
	status = "okay";
};

&reg_usb2_vbus {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 4 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PH4 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	usb2_vbus-supply = <&reg_usb2_vbus>;
	status = "okay";
};
