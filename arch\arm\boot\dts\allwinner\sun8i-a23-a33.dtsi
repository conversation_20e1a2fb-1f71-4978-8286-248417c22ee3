/*
 * Copyright 2014 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

#include <dt-bindings/clock/sun6i-rtc.h>
#include <dt-bindings/clock/sun8i-a23-a33-ccu.h>
#include <dt-bindings/reset/sun8i-a23-a33-ccu.h>

/ {
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	chosen {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		simplefb_lcd: framebuffer-lcd0 {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "de_be0-lcd0";
			clocks = <&ccu CLK_BUS_LCD>, <&ccu CLK_BUS_DE_BE>,
				 <&ccu CLK_LCD_CH0>, <&ccu CLK_DE_BE>,
				 <&ccu CLK_DRAM_DE_BE>, <&ccu CLK_DRC>;
			status = "disabled";
		};
	};

	de: display-engine {
		/* compatible gets set in SoC specific dtsi file */
		allwinner,pipelines = <&fe0>;
		status = "disabled";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		clock-frequency = <24000000>;
		arm,cpu-registers-not-fw-configured;
	};

	cpus {
		enable-method = "allwinner,sun8i-a23";
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <1>;
		};
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: osc24M_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-accuracy = <50000>;
			clock-output-names = "osc24M";
		};

		ext_osc32k: ext_osc32k_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-accuracy = <50000>;
			clock-output-names = "ext-osc32k";
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		system-control@1c00000 {
			compatible = "allwinner,sun8i-a23-system-control";
			reg = <0x01c00000 0x30>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			sram_c: sram@1d00000 {
				compatible = "mmio-sram";
				reg = <0x01d00000 0x80000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x01d00000 0x80000>;

				ve_sram: sram-section@0 {
					compatible = "allwinner,sun8i-a23-sram-c1",
						     "allwinner,sun4i-a10-sram-c1";
					reg = <0x000000 0x80000>;
				};
			};
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun8i-a23-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DMA>;
			resets = <&ccu RST_BUS_DMA>;
			#dma-cells = <1>;
		};

		nfc: nand-controller@1c03000 {
			compatible = "allwinner,sun8i-a23-nand-controller";
			reg = <0x01c03000 0x1000>;
			interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_NAND>, <&ccu CLK_NAND>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_NAND>;
			reset-names = "ahb";
			dmas = <&dma 5>;
			dma-names = "rxtx";
			pinctrl-names = "default";
			pinctrl-0 = <&nand_pins &nand_cs0_pin &nand_rb0_pin>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		tcon0: lcd-controller@1c0c000 {
			/* compatible gets set in SoC specific dtsi file */
			reg = <0x01c0c000 0x1000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			dmas = <&dma 12>;
			clocks = <&ccu CLK_BUS_LCD>,
				 <&ccu CLK_LCD_CH0>,
				 <&ccu 13>;
			clock-names = "ahb",
				      "tcon-ch0",
				      "lvds-alt";
			clock-output-names = "tcon-data-clock";
			#clock-cells = <0>;
			resets = <&ccu RST_BUS_LCD>,
				 <&ccu RST_BUS_LVDS>;
			reset-names = "lcd",
				      "lvds";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					reg = <0>;

					tcon0_in_drc0: endpoint {
						remote-endpoint = <&drc0_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					reg = <1>;
				};
			};
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC0>,
				 <&ccu CLK_MMC0>,
				 <&ccu CLK_MMC0_OUTPUT>,
				 <&ccu CLK_MMC0_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC0>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC1>,
				 <&ccu CLK_MMC1>,
				 <&ccu CLK_MMC1_OUTPUT>,
				 <&ccu CLK_MMC1_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC1>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun7i-a20-mmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC2>,
				 <&ccu CLK_MMC2>,
				 <&ccu CLK_MMC2_OUTPUT>,
				 <&ccu CLK_MMC2_SAMPLE>;
			clock-names = "ahb",
				      "mmc",
				      "output",
				      "sample";
			resets = <&ccu RST_BUS_MMC2>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		usb_otg: usb@1c19000 {
			/* compatible gets set in SoC specific dtsi file */
			reg = <0x01c19000 0x0400>;
			clocks = <&ccu CLK_BUS_OTG>;
			resets = <&ccu RST_BUS_OTG>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			dr_mode = "otg";
			status = "disabled";
		};

		usbphy: phy@1c19400 {
			/*
			 * compatible and address regions get set in
			 * SoC specific dtsi file
			 */
			clocks = <&ccu CLK_USB_PHY0>,
				 <&ccu CLK_USB_PHY1>;
			clock-names = "usb0_phy",
				      "usb1_phy";
			resets = <&ccu RST_USB_PHY0>,
				 <&ccu RST_USB_PHY1>;
			reset-names = "usb0_reset",
				      "usb1_reset";
			status = "disabled";
			#phy-cells = <1>;
		};

		ehci0: usb@1c1a000 {
			compatible = "allwinner,sun8i-a23-ehci", "generic-ehci";
			reg = <0x01c1a000 0x100>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI>;
			resets = <&ccu RST_BUS_EHCI>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci0: usb@1c1a400 {
			compatible = "allwinner,sun8i-a23-ohci", "generic-ohci";
			reg = <0x01c1a400 0x100>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_OHCI>, <&ccu CLK_USB_OHCI>;
			resets = <&ccu RST_BUS_OHCI>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ccu: clock@1c20000 {
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&rtc CLK_OSC32K>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			/* compatible gets set in SoC specific dtsi file */
			reg = <0x01c20800 0x400>;
			interrupt-parent = <&r_intc>;
			/* interrupts get set in SoC specific dtsi file */
			clocks = <&ccu CLK_BUS_PIO>, <&osc24M>,
				 <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			i2c0_pins: i2c0-pins {
				pins = "PH2", "PH3";
				function = "i2c0";
			};

			i2c1_pins: i2c1-pins {
				pins = "PH4", "PH5";
				function = "i2c1";
			};

			i2c2_pins: i2c2-pins {
				pins = "PE12", "PE13";
				function = "i2c2";
			};

			lcd_rgb666_pins: lcd-rgb666-pins {
				pins = "PD2", "PD3", "PD4", "PD5", "PD6", "PD7",
				       "PD10", "PD11", "PD12", "PD13", "PD14", "PD15",
				       "PD18", "PD19", "PD20", "PD21", "PD22", "PD23",
				       "PD24", "PD25", "PD26", "PD27";
				function = "lcd0";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2",
				       "PF3", "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc1_pg_pins: mmc1-pg-pins {
				pins = "PG0", "PG1", "PG2",
				       "PG3", "PG4", "PG5";
				function = "mmc1";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_8bit_pins: mmc2-8bit-pins {
				pins = "PC5", "PC6", "PC8",
				       "PC9", "PC10", "PC11",
				       "PC12", "PC13", "PC14",
				       "PC15", "PC16";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			nand_pins: nand-pins {
				pins = "PC0", "PC1", "PC2", "PC5",
				       "PC8", "PC9", "PC10", "PC11",
				       "PC12", "PC13", "PC14", "PC15";
				function = "nand0";
			};

			nand_cs0_pin: nand-cs0-pin {
				pins = "PC4";
				function = "nand0";
				bias-pull-up;
			};

			nand_cs1_pin: nand-cs1-pin {
				pins = "PC3";
				function = "nand0";
				bias-pull-up;
			};

			nand_rb0_pin: nand-rb0-pin {
				pins = "PC6";
				function = "nand0";
				bias-pull-up;
			};

			nand_rb1_pin: nand-rb1-pin {
				pins = "PC7";
				function = "nand0";
				bias-pull-up;
			};

			pwm0_pin: pwm0-pin {
				pins = "PH0";
				function = "pwm0";
			};

			uart0_pf_pins: uart0-pf-pins {
				pins = "PF2", "PF4";
				function = "uart0";
			};

			uart1_pg_pins: uart1-pg-pins {
				pins = "PG6", "PG7";
				function = "uart1";
			};

			uart1_cts_rts_pg_pins: uart1-cts-rts-pg-pins {
				pins = "PG8", "PG9";
				function = "uart1";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun8i-a23-timer";
			reg = <0x01c20c00 0xa0>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		wdt0: watchdog@1c20ca0 {
			compatible = "allwinner,sun6i-a31-wdt";
			reg = <0x01c20ca0 0x20>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		pwm: pwm@1c21400 {
			compatible = "allwinner,sun7i-a20-pwm";
			reg = <0x01c21400 0xc>;
			clocks = <&osc24M>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		lradc: lradc@1c22800 {
			compatible = "allwinner,sun4i-a10-lradc-keys";
			reg = <0x01c22800 0x100>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART0>;
			resets = <&ccu RST_BUS_UART0>;
			dmas = <&dma 6>, <&dma 6>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART1>;
			resets = <&ccu RST_BUS_UART1>;
			dmas = <&dma 7>, <&dma 7>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART2>;
			resets = <&ccu RST_BUS_UART2>;
			dmas = <&dma 8>, <&dma 8>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart3: serial@1c28c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28c00 0x400>;
			interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART3>;
			resets = <&ccu RST_BUS_UART3>;
			dmas = <&dma 9>, <&dma 9>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart4: serial@1c29000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29000 0x400>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART4>;
			resets = <&ccu RST_BUS_UART4>;
			dmas = <&dma 10>, <&dma 10>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C0>;
			resets = <&ccu RST_BUS_I2C0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C1>;
			resets = <&ccu RST_BUS_I2C1>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@1c2b400 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b400 0x400>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C2>;
			resets = <&ccu RST_BUS_I2C2>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mali: gpu@1c40000 {
			compatible = "allwinner,sun8i-a23-mali",
				     "allwinner,sun7i-a20-mali", "arm,mali-400";
			reg = <0x01c40000 0x10000>;
			interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gp",
					  "gpmmu",
					  "pp0",
					  "ppmmu0",
					  "pp1",
					  "ppmmu1",
					  "pmu";
			clocks = <&ccu CLK_BUS_GPU>, <&ccu CLK_GPU>;
			clock-names = "bus", "core";
			resets = <&ccu RST_BUS_GPU>;
			#cooling-cells = <2>;

			assigned-clocks = <&ccu CLK_GPU>;
			assigned-clock-rates = <384000000>;
		};

		gic: interrupt-controller@1c81000 {
			compatible = "arm,gic-400";
			reg = <0x01c81000 0x1000>,
			      <0x01c82000 0x2000>,
			      <0x01c84000 0x2000>,
			      <0x01c86000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		fe0: display-frontend@1e00000 {
			/* compatible gets set in SoC specific dtsi file */
			reg = <0x01e00000 0x20000>;
			interrupts = <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DE_FE>, <&ccu CLK_DE_FE>,
				 <&ccu CLK_DRAM_DE_FE>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_BUS_DE_FE>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				fe0_out: port@1 {
					reg = <1>;

					fe0_out_be0: endpoint {
						remote-endpoint = <&be0_in_fe0>;
					};
				};
			};
		};

		be0: display-backend@1e60000 {
			/* compatible gets set in SoC specific dtsi file */
			reg = <0x01e60000 0x10000>;
			interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DE_BE>, <&ccu CLK_DE_BE>,
				 <&ccu CLK_DRAM_DE_BE>;
			clock-names = "ahb", "mod",
				      "ram";
			resets = <&ccu RST_BUS_DE_BE>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				be0_in: port@0 {
					reg = <0>;

					be0_in_fe0: endpoint {
						remote-endpoint = <&fe0_out_be0>;
					};
				};

				be0_out: port@1 {
					reg = <1>;

					be0_out_drc0: endpoint {
						remote-endpoint = <&drc0_in_be0>;
					};
				};
			};
		};

		drc0: drc@1e70000 {
			/* compatible gets set in SoC specific dtsi file */
			reg = <0x01e70000 0x10000>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DRC>, <&ccu CLK_DRC>,
				 <&ccu CLK_DRAM_DRC>;
			clock-names = "ahb", "mod", "ram";
			resets = <&ccu RST_BUS_DRC>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				drc0_in: port@0 {
					reg = <0>;

					drc0_in_be0: endpoint {
						remote-endpoint = <&be0_out_drc0>;
					};
				};

				drc0_out: port@1 {
					reg = <1>;

					drc0_out_tcon0: endpoint {
						remote-endpoint = <&tcon0_in_drc0>;
					};
				};
			};
		};

		rtc: rtc@1f00000 {
			compatible = "allwinner,sun8i-a23-rtc";
			reg = <0x01f00000 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
			clock-output-names = "osc32k", "osc32k-out";
			clocks = <&ext_osc32k>;
			#clock-cells = <1>;
		};

		r_intc: interrupt-controller@1f00c00 {
			compatible = "allwinner,sun6i-a31-r-intc";
			interrupt-controller;
			#interrupt-cells = <3>;
			reg = <0x01f00c00 0x400>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		};

		prcm@1f01400 {
			compatible = "allwinner,sun8i-a23-prcm";
			reg = <0x01f01400 0x200>;

			ar100: ar100_clk {
				compatible = "fixed-factor-clock";
				#clock-cells = <0>;
				clock-div = <1>;
				clock-mult = <1>;
				clocks = <&osc24M>;
				clock-output-names = "ar100";
			};

			ahb0: ahb0_clk {
				compatible = "fixed-factor-clock";
				#clock-cells = <0>;
				clock-div = <1>;
				clock-mult = <1>;
				clocks = <&ar100>;
				clock-output-names = "ahb0";
			};

			apb0: apb0_clk {
				compatible = "allwinner,sun8i-a23-apb0-clk";
				#clock-cells = <0>;
				clocks = <&ahb0>;
				clock-output-names = "apb0";
			};

			apb0_gates: apb0_gates_clk {
				compatible = "allwinner,sun8i-a23-apb0-gates-clk";
				#clock-cells = <1>;
				clocks = <&apb0>;
				clock-output-names = "apb0_pio", "apb0_timer",
						"apb0_rsb", "apb0_uart",
						"apb0_i2c";
			};

			apb0_rst: apb0_rst {
				compatible = "allwinner,sun6i-a31-clock-reset";
				#reset-cells = <1>;
			};

			codec_analog: codec-analog {
				compatible = "allwinner,sun8i-a23-codec-analog";
			};
		};

		cpucfg@1f01c00 {
			compatible = "allwinner,sun8i-a23-cpuconfig";
			reg = <0x01f01c00 0x300>;
		};

		r_uart: serial@1f02800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01f02800 0x400>;
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&apb0_gates 4>;
			resets = <&apb0_rst 4>;
			status = "disabled";
		};

		r_i2c: i2c@1f02400 {
			compatible = "allwinner,sun8i-a23-i2c",
				     "allwinner,sun6i-a31-i2c";
			reg = <0x01f02400 0x400>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_i2c_pins>;
			clocks = <&apb0_gates 6>;
			resets = <&apb0_rst 6>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		r_pio: pinctrl@1f02c00 {
			compatible = "allwinner,sun8i-a23-r-pinctrl";
			reg = <0x01f02c00 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&apb0_gates 0>, <&osc24M>, <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			r_i2c_pins: r-i2c-pins {
				pins = "PL0", "PL1";
				function = "s_i2c";
				bias-pull-up;
			};

			r_rsb_pins: r-rsb-pins {
				pins = "PL0", "PL1";
				function = "s_rsb";
				drive-strength = <20>;
				bias-pull-up;
			};

			r_uart_pins_a: r-uart-pins {
				pins = "PL2", "PL3";
				function = "s_uart";
			};
		};

		r_rsb: rsb@1f03400 {
			compatible = "allwinner,sun8i-a23-rsb";
			reg = <0x01f03400 0x400>;
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&apb0_gates 3>;
			clock-frequency = <3000000>;
			resets = <&apb0_rst 3>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_rsb_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};
	};
};
