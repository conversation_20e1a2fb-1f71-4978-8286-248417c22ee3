/*
 * Copyright 2014 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "sun8i-a23-a33.dtsi"

/ {
	soc {
		codec: codec@1c22c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-a23-codec";
			reg = <0x01c22c00 0x400>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CODEC>, <&ccu CLK_AC_DIG>;
			clock-names = "apb", "codec";
			resets = <&ccu RST_BUS_CODEC>;
			dmas = <&dma 15>, <&dma 15>;
			dma-names = "rx", "tx";
			allwinner,codec-analog-controls = <&codec_analog>;
			status = "disabled";
		};
	};
};

&be0 {
	compatible = "allwinner,sun8i-a23-display-backend";
};

&ccu {
	compatible = "allwinner,sun8i-a23-ccu";
};

&de {
	compatible = "allwinner,sun8i-a23-display-engine";
};

&drc0 {
	compatible = "allwinner,sun8i-a23-drc";
};

&fe0 {
	compatible = "allwinner,sun8i-a23-display-frontend";
};

&pio {
	compatible = "allwinner,sun8i-a23-pinctrl";
	interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
};

&tcon0 {
	compatible = "allwinner,sun8i-a23-tcon";
};

&usb_otg {
	compatible = "allwinner,sun6i-a31-musb";
};

&usbphy {
	compatible = "allwinner,sun8i-a23-usb-phy";
	reg = <0x01c19400 0x10>, <0x01c1a800 0x4>;
	reg-names = "phy_ctrl", "pmu1";
};
