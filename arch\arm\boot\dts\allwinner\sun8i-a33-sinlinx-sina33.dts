/*
 * Copyright 2015 <PERSON><PERSON><PERSON>
 *
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun8i-a33.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "Sinlinx SinA33";
	compatible = "sinlinx,sina33", "allwinner,sun8i-a33";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	panel {
		compatible = "netron-dy,e231732";
		power-supply = <&reg_vcc3v3>;

		port {
			panel_input: endpoint {
				remote-endpoint = <&tcon0_out_panel>;
			};
		};
	};
};

&codec {
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc3>;
};

&cpu0_opp_table {
	opp-1104000000 {
		opp-hz = /bits/ 64 <1104000000>;
		opp-microvolt = <1320000>;
		clock-latency-ns = <244144>; /* 8 32k periods */
	};

	opp-1200000000 {
		opp-hz = /bits/ 64 <1200000000>;
		opp-microvolt = <1320000>;
		clock-latency-ns = <244144>; /* 8 32k periods */
	};
};

&de {
	status = "okay";
};

&dai {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&lradc {
	vref-supply = <&reg_dcdc1>;
	status = "okay";

	button-200 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <191011>;
	};

	button-400 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <391304>;
	};

	button-600 {
		label = "Home";
		linux,code = <KEY_HOME>;
		channel = <0>;
		voltage = <600000>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 1 4 GPIO_ACTIVE_LOW>; /* PB4 */
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&mmc2_8bit_pins {
	/* Increase drive strength for DDR modes */
	drive-strength = <40>;
};

&ohci0 {
	status = "okay";
};

&r_rsb {
	status = "okay";

	axp22x: pmic@3a3 {
		compatible = "x-powers,axp223";
		reg = <0x3a3>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		eldoin-supply = <&reg_dcdc1>;
	};
};

#include "axp223.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&reg_aldo1 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "vcc-io";
};

&reg_aldo2 {
	regulator-always-on;
	regulator-min-microvolt = <2350000>;
	regulator-max-microvolt = <2650000>;
	regulator-name = "vdd-dll";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <2700000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-pll-avcc";
};

&reg_dc5ldo {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpus";
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "vcc-3v0";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-sys";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

&reg_rtc_ldo {
	regulator-name = "vcc-rtc";
};

&sound {
	status = "okay";
};

&tcon0 {
	pinctrl-names = "default";
	pinctrl-0 = <&lcd_rgb666_pins>;
	status = "okay";
};

&tcon0_out {
	tcon0_out_panel: endpoint@0 {
		reg = <0>;
		remote-endpoint = <&panel_input>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	status = "okay";
	usb1_vbus-supply = <&reg_vcc5v0>; /* USB1 VBUS is always on */
};
