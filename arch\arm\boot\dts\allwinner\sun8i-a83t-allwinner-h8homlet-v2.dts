/*
 * Copyright 2015 <PERSON>
 * <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun8i-a83t.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Allwinner A83T H8Homlet Proto Dev Board v2.0";
	compatible = "allwinner,h8homlet-v2", "allwinner,sun8i-a83t";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	reg_usb0_vbus: reg-usb0-vbus {
		compatible = "regulator-fixed";
		regulator-name = "usb0-vbus";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
		enable-active-high;
		gpio = <&r_pio 0 5 GPIO_ACTIVE_HIGH>; /* PL5 */
	};

	reg_usb1_vbus: reg-usb1-vbus {
		compatible = "regulator-fixed";
		regulator-name = "usb1-vbus";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
		enable-active-high;
		gpio = <&r_pio 0 6 GPIO_ACTIVE_HIGH>; /* PL6 */
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&cpu100 {
	cpu-supply = <&reg_dcdc3>;
};

&ehci0 {
	status = "okay";
};

&mmc0 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc0_pins>;
	vmmc-supply = <&reg_dcdc1>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	bus-width = <4>;
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_emmc_pins>;
	vmmc-supply = <&reg_dcdc1>;
	vqmmc-supply = <&reg_dcdc1>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&r_rsb {
	status = "okay";

	axp81x: pmic@3a3 {
		compatible = "x-powers,axp818", "x-powers,axp813";
		reg = <0x3a3>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		eldoin-supply = <&reg_dcdc1>;
		swin-supply = <&reg_dcdc1>;
	};

	ac100: codec@e89 {
		compatible = "x-powers,ac100";
		reg = <0xe89>;

		ac100_codec: codec {
			compatible = "x-powers,ac100-codec";
			interrupt-parent = <&r_pio>;
			interrupts = <0 11 IRQ_TYPE_LEVEL_LOW>; /* PL11 */
			#clock-cells = <0>;
			clock-output-names = "4M_adda";
		};

		ac100_rtc: rtc {
			compatible = "x-powers,ac100-rtc";
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
			clocks = <&ac100_codec>;
			#clock-cells = <1>;
			clock-output-names = "cko1_rtc",
					     "cko2_rtc",
					     "cko3_rtc";
		};
	};
};

#include "axp81x.dtsi"

&ac_power_supply {
	status = "okay";
};

&reg_aldo1 {
	regulator-always-on;
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "vcc-1v8";
};

&reg_aldo2 {
	regulator-always-on;
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "dram-pll";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-3v3";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-cpua";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-cpub";
};

&reg_dcdc4 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-gpu";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

&reg_dcdc6 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <900000>;
	regulator-name = "vdd-sys";
};

&reg_dldo2 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-mipi";
};

&reg_dldo4 {
	/*
	 * The PHY requires 20ms after all voltages are applied until core
	 * logic is ready and 30ms after the reset pin is de-asserted.
	 * Set a 100ms delay to account for PMIC ramp time and board traces.
	 */
	regulator-enable-ramp-delay = <100000>;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-ephy";
};

&reg_fldo1 {
	regulator-min-microvolt = <1080000>;
	regulator-max-microvolt = <1320000>;
	regulator-name = "vdd12-hsic";
};

&reg_fldo2 {
	/*
	 * Despite the embedded CPUs core not being used in any way,
	 * this must remain on or the system will hang.
	 */
	regulator-always-on;
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-cpus";
};

&reg_rtc_ldo {
	regulator-name = "vcc-rtc";
};

&reg_sw {
	regulator-name = "vcc-wifi";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&usbphy {
	usb0_vbus-supply = <&reg_usb0_vbus>;
	usb1_vbus-supply = <&reg_usb1_vbus>;
	status = "okay";
};

&usb_otg {
	dr_mode = "host";
	status = "okay";
};
