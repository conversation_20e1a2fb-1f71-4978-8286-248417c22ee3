/*
 * Copyright (C) 2017 Touchless Biometric Systems AG
 * Tomas <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun8i-a83t.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pwm/pwm.h>
#include <dt-bindings/input/input.h>

/ {
	model = "TBS A711 Tablet";
	compatible = "tbs-biometrics,a711", "allwinner,sun8i-a83t";

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm 0 50000 PWM_POLARITY_INVERTED>;
		enable-gpios = <&pio 3 29 GPIO_ACTIVE_HIGH>;
		power-supply = <&reg_sw>;
		brightness-levels = <0 1 2 4 8 16 32 64 128 255>;
		default-brightness-level = <9>;
	};

	panel {
		compatible = "tbs,a711-panel", "panel-lvds";
		backlight = <&backlight>;
		power-supply = <&reg_sw>;

		width-mm = <153>;
		height-mm = <90>;
		data-mapping = "vesa-24";

		panel-timing {
			/* 1024x600 @60Hz */
			clock-frequency = <52000000>;
			hactive = <1024>;
			vactive = <600>;
			hsync-len = <20>;
			hfront-porch = <180>;
			hback-porch = <160>;
			vfront-porch = <12>;
			vback-porch = <23>;
			vsync-len = <5>;
		};

		port {
			panel_input: endpoint {
				remote-endpoint = <&tcon0_out_lcd>;
			};
		};
	};

	reg_gps: reg-gps {
		compatible = "regulator-fixed";
		regulator-name = "gps";
		regulator-min-microvolt = <3000000>;
		regulator-max-microvolt = <3000000>;
	};

	reg_vbat: reg-vbat {
		compatible = "regulator-fixed";
		regulator-name = "vbat";
		regulator-min-microvolt = <3700000>;
		regulator-max-microvolt = <3700000>;
	};

	reg_vmain: reg-vmain {
		compatible = "regulator-fixed";
		regulator-name = "vmain";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		gpio = <&r_pio 0 9 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&reg_vbat>;
	};

	wifi_pwrseq: wifi_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&r_pio 0 2 GPIO_ACTIVE_LOW>; /* PL2 WL-PMU-EN */

		/*
		 * This is actually Bluetooth's clock, but we have to
		 * hook it up somewheere
		 */
		clocks = <&ac100_rtc 1>;
		clock-names = "ext_clock";
	};
};

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&cpu100 {
	cpu-supply = <&reg_dcdc3>;
};

&de {
	status = "okay";
};

/*
 * An USB-2 hub is connected here, which also means we don't need to
 * enable the OHCI controller.
 */
&ehci0 {
	status = "okay";
};

/*
 * There's a modem connected here that needs to be initialised before
 * being able to be enumerated.
 */
&ehci1 {
	status = "okay";
};

&i2c0 {
	clock-frequency = <400000>;
	status = "okay";

	touchscreen@38 {
		compatible = "edt,edt-ft5206";
		reg = <0x38>;
		interrupt-parent = <&r_pio>;
		interrupts = <0 7 IRQ_TYPE_EDGE_FALLING>; /* PL7 */
		reset-gpios = <&pio 3 5 GPIO_ACTIVE_LOW>; /* PD5 */
		vcc-supply = <&reg_ldo_io0>;
		touchscreen-size-x = <1024>;
		touchscreen-size-y = <600>;
	};
};

&i2c1 {
	clock-frequency = <400000>;
	status = "okay";

	accelerometer@18 {
		compatible = "bosch,bma250";
		reg = <0x18>;
		interrupt-parent = <&pio>;
		interrupts = <7 10 IRQ_TYPE_EDGE_RISING>; /* PH10 / EINT10 */
	};
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	pinctrl-names = "default";
	pinctrl-0 = <&mmc0_pins>;
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&mmc1 {
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	vmmc-supply = <&reg_dldo1>;
	vqmmc-supply = <&reg_dldo1>;
	non-removable;
	wakeup-source;
	keep-power-in-suspend;
	status = "okay";

	brcmf: wifi@1 {
		reg = <1>;
		compatible = "brcm,bcm4329-fmac";
		interrupt-parent = <&r_pio>;
		interrupts = <0 3 IRQ_TYPE_LEVEL_LOW>; /* PL3 WL_WAKE_UP */
		interrupt-names = "host-wake";
	};
};

&mmc2 {
	pinctrl-0 = <&mmc2_8bit_emmc_pins>;
	pinctrl-names = "default";
	vmmc-supply = <&reg_dcdc1>;
	vqmmc-supply = <&reg_dcdc1>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&pwm {
	pinctrl-names = "default";
	pinctrl-0 = <&pwm_pin>;
	status = "okay";
};

&r_lradc {
	vref-supply = <&reg_aldo2>;
	status = "okay";

	button-210 {
		label = "Volume Up";
		linux,code = <KEY_VOLUMEUP>;
		channel = <0>;
		voltage = <210000>;
	};

	button-410 {
		label = "Volume Down";
		linux,code = <KEY_VOLUMEDOWN>;
		channel = <0>;
		voltage = <410000>;
	};
};

&r_rsb {
	status = "okay";

	axp81x: pmic@3a3 {
		compatible = "x-powers,axp813";
		reg = <0x3a3>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		swin-supply = <&reg_dcdc1>;
		x-powers,drive-vbus-en;
	};

	ac100: codec@e89 {
		compatible = "x-powers,ac100";
		reg = <0xe89>;

		ac100_codec: codec {
			compatible = "x-powers,ac100-codec";
			interrupt-parent = <&r_pio>;
			interrupts = <0 12 IRQ_TYPE_LEVEL_LOW>; /* PL12 */
			#clock-cells = <0>;
			clock-output-names = "4M_adda";
		};

		ac100_rtc: rtc {
			compatible = "x-powers,ac100-rtc";
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
			clocks = <&ac100_codec>;
			#clock-cells = <1>;
			clock-output-names = "cko1_rtc",
					     "cko2_rtc",
					     "cko3_rtc";
		};
	};

};

#include "axp81x.dtsi"

&battery_power_supply {
	status = "okay";
};

&reg_aldo1 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "vcc-1.8";
};

&reg_aldo2 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-always-on;
	regulator-name = "vdd-drampll";
};

&reg_aldo3 {
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-always-on;
	regulator-name = "avcc";
};

&reg_dcdc1 {
	regulator-min-microvolt = <3100000>;
	regulator-max-microvolt = <3100000>;
	regulator-always-on;
	regulator-name = "vcc-io";
};

&reg_dcdc2 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-always-on;
	regulator-name = "vdd-cpu-A";
};

&reg_dcdc3 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-always-on;
	regulator-name = "vdd-cpu-B";
};

&reg_dcdc4 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-gpu";
};

&reg_dcdc5 {
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1500000>;
	regulator-always-on;
	regulator-name = "vcc-dram";
};

&reg_dcdc6 {
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <900000>;
	regulator-always-on;
	regulator-name = "vdd-sys";
};

&reg_dldo1 {
	regulator-min-microvolt = <3100000>;
	regulator-max-microvolt = <3100000>;
	regulator-name = "vcc-wifi-io";
};

&reg_dldo2 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <4200000>;
	regulator-name = "vcc-mipi";
};

&reg_dldo3 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "vdd-csi";
};

&reg_dldo4 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-name = "avdd-csi";
};

&reg_drivevbus {
	regulator-name = "usb0-vbus";
	status = "okay";
};

&reg_eldo1 {
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "dvdd-csi-r";
};

&reg_eldo2 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "vcc-dsi";
};

&reg_eldo3 {
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "dvdd-csi-f";
};

&reg_fldo1 {
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1200000>;
	regulator-name = "vcc-hsic";
};

&reg_fldo2 {
	regulator-min-microvolt = <700000>;
	regulator-max-microvolt = <1100000>;
	regulator-always-on;
	regulator-name = "vdd-cpus";
};

&reg_ldo_io0 {
	regulator-min-microvolt = <3100000>;
	regulator-max-microvolt = <3100000>;
	regulator-name = "vcc-ctp";
	status = "okay";
};

&reg_ldo_io1 {
	regulator-min-microvolt = <3100000>;
	regulator-max-microvolt = <3100000>;
	regulator-name = "vcc-vb";
	status = "okay";
};

&reg_sw {
	regulator-min-microvolt = <3100000>;
	regulator-max-microvolt = <3100000>;
	regulator-name = "vcc-lcd";
};

&tcon0 {
	pinctrl-names = "default";
	pinctrl-0 = <&lcd_lvds_pins>;
};

&tcon0_out {
	tcon0_out_lcd: endpoint {
		remote-endpoint = <&panel_input>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

/* There's the BT part of the AP6210 connected to that UART */
&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>, <&uart1_rts_cts_pins>;
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm20702a1";
		clocks = <&ac100_rtc 1>;
		clock-names = "lpo";
		vbat-supply = <&reg_vbat>;
		vddio-supply = <&reg_dldo1>;
		device-wakeup-gpios = <&pio 7 5 GPIO_ACTIVE_HIGH>; /* PH5 */
		host-wakeup-gpios = <&r_pio 0 5 GPIO_ACTIVE_HIGH>; /* PL5 */
		shutdown-gpios = <&r_pio 0 4 GPIO_ACTIVE_HIGH>; /* PL4 */
		max-speed = <1500000>;
	};
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pb_pins>;
	status = "okay";

	gnss {
		compatible = "u-blox,neo-6m";

		v-bckp-supply = <&reg_rtc_ldo>;
		vcc-supply = <&reg_gps>;
		current-speed = <9600>;
	};
};

&usb_otg {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 11 (GPIO_ACTIVE_HIGH | GPIO_PULL_UP)>; /* PH11 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_drivevbus>;
	usb1_vbus-supply = <&reg_vmain>;
	usb2_vbus-supply = <&reg_vmain>;
	status = "okay";
};
