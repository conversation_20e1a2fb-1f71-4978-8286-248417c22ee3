// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * DTS for Emlid Neutis N5 Dev board.
 *
 * Copyright (C) 2019 <PERSON><PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "sun8i-h3-emlid-neutis-n5h3.dtsi"

/ {
	model = "Emlid Neutis N5H3 Developer board";
	compatible = "emlid,neutis-n5h3-devboard",
		     "emlid,neutis-n5h3",
		     "allwinner,sun8i-h3";

	vdd_cpux: gpio-regulator {
		compatible = "regulator-gpio";
		regulator-name = "vdd-cpux";
		regulator-type = "voltage";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1100000>;
		regulator-max-microvolt = <1300000>;
		regulator-ramp-delay = <50>; /* 4ms */
		gpios = <&r_pio 0 6 GPIO_ACTIVE_HIGH>; /* PL6 */
		gpios-states = <0x1>;
		states = <1100000 0x0>, <1300000 0x1>;
	};

	connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

};

&cpu0 {
	cpu-supply = <&vdd_cpux>;
};

&codec {
	status = "okay";
};

&emac {
	phy-handle = <&int_mii_phy>;
	phy-mode = "mii";
	allwinner,leds-active-low;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c1 {
	status = "okay";
};
