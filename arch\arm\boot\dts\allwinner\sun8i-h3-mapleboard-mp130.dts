// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (C) 2017 Centrum Embedded Systems, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2018 <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun8i-h3.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "MapleBoard MP130";
	compatible = "mapleboard,mp130", "allwinner,sun8i-h3";

	aliases {
		ethernet0 = &emac;
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		pwr_led {
			label = "mp130:orange:pwr";
			gpios = <&r_pio 0 10 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};

		status_led {
			label = "mp130:orange:status";
			gpios = <&pio 0 10 GPIO_ACTIVE_HIGH>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-power {
			label = "power";
			linux,code = <KEY_POWER>;
			gpios = <&r_pio 0 3 GPIO_ACTIVE_LOW>; /* PL3 */
		};

		key-user {
			label = "user";
			linux,code = <BTN_0>;
			gpios = <&r_pio 0 4 GPIO_ACTIVE_LOW>;
		};
	};
};

&codec {
	allwinner,audio-routing =
		"Line Out", "LINEOUT",
		"LINEIN", "Line In";
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ehci2 {
	status = "okay";
};

&ehci3 {
	status = "okay";
};

&emac {
	phy-handle = <&int_mii_phy>;
	phy-mode = "mii";
	allwinner,leds-active-low;
	status = "okay";
};

&ir {
	pinctrl-names = "default";
	pinctrl-0 = <&r_ir_rx_pin>;
	status = "okay";
};

&mmc0 {
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&ohci2 {
	status = "okay";
};

&ohci3 {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pa_pins>;
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pins>;
	status = "disabled";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pins>;
	status = "disabled";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pins>;
	status = "disabled";
};

&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	/* USB VBUS is always on */
	status = "okay";
};
