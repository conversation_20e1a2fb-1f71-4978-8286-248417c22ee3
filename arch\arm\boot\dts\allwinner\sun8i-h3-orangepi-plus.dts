/*
 * Copyright (C) 2015 <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/* The Orange Pi Plus is an extended version of the Orange Pi 2 */
#include "sun8i-h3-orangepi-2.dts"

/ {
	model = "Xunlong Orange Pi Plus / Plus 2";
	compatible = "xunlong,orangepi-plus", "allwinner,sun8i-h3";

	aliases {
		ethernet0 = &emac;
	};

	reg_gmac_3v3: gmac-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "gmac-3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		startup-delay-us = <100000>;
		enable-active-high;
		gpio = <&pio 3 6 GPIO_ACTIVE_HIGH>;
	};

	reg_usb3_vbus: usb3-vbus {
		compatible = "regulator-fixed";
		regulator-name = "usb3-vbus";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-boot-on;
		enable-active-high;
		gpio = <&pio 6 11 GPIO_ACTIVE_HIGH>;
	};
};

&cpu0 {
	cpu-supply = <&reg_vdd_cpux>;
};

&ehci3 {
	status = "okay";
};

&emac {
	pinctrl-names = "default";
	pinctrl-0 = <&emac_rgmii_pins>;
	phy-supply = <&reg_gmac_3v3>;
	phy-handle = <&ext_rgmii_phy>;
	phy-mode = "rgmii-id";

	status = "okay";
};

&external_mdio {
	ext_rgmii_phy: ethernet-phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0>;
	};
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&mmc2_8bit_pins {
	/* Increase drive strength for DDR modes */
	drive-strength = <40>;
	/* eMMC is missing pull-ups */
	bias-pull-up;
};

&r_i2c {
	status = "okay";

	reg_vdd_cpux: regulator@65 {
		compatible = "silergy,sy8106a";
		reg = <0x65>;
		regulator-name = "vdd-cpux";
		silergy,fixed-microvolt = <1200000>;
		regulator-min-microvolt = <1000000>;
		regulator-max-microvolt = <1400000>;
		regulator-ramp-delay = <200>;
		regulator-boot-on;
		regulator-always-on;
	};
};

&usbphy {
	usb3_vbus-supply = <&reg_usb3_vbus>;
};
