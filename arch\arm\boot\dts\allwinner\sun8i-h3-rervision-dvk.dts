// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2019 Bootlin
 * Author: <PERSON> <paul.koc<PERSON><PERSON>@bootlin.com>
 */

/dts-v1/;
#include "sun8i-h3.dtsi"
#include "sunxi-common-regulators.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "RerVision H3-DVK";
	compatible = "rervision,h3-dvk", "allwinner,sun8i-h3";

	aliases {
		ethernet0 = &emac;
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};
};

&de {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ehci2 {
	status = "okay";
};

&ehci3 {
	status = "okay";
};

&emac {
	phy-handle = <&int_mii_phy>;
	phy-mode = "mii";
	allwinner,leds-active-low;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&mmc0 {
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	status = "okay";
	vmmc-supply = <&reg_vcc3v3>;
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_vcc3v3>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&ohci2 {
	status = "okay";
};

&ohci3 {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pa_pins>;
	status = "okay";
};

&usb_otg {
	status = "okay";
	dr_mode = "peripheral";
};

&usbphy {
	status = "okay";
};
