/*
 * Copyright (C) 2015 <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include "sunxi-h3-h5.dtsi"
#include <dt-bindings/thermal/thermal.h>

/ {
	cpu0_opp_table: opp-table-cpu {
		compatible = "operating-points-v2";
		opp-shared;

		opp-648000000 {
			opp-hz = /bits/ 64 <648000000>;
			opp-microvolt = <1040000 1040000 1300000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <1100000 1100000 1300000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};

		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <1200000 1200000 1300000>;
			clock-latency-ns = <244144>; /* 8 32k periods */
		};
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <1>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};

		cpu2: cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <2>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};

		cpu3: cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <3>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
			operating-points-v2 = <&cpu0_opp_table>;
			#cooling-cells = <2>;
		};
	};

	gpu_opp_table: opp-table-gpu {
		compatible = "operating-points-v2";

		opp-120000000 {
			opp-hz = /bits/ 64 <120000000>;
		};

		opp-312000000 {
			opp-hz = /bits/ 64 <312000000>;
		};

		opp-432000000 {
			opp-hz = /bits/ 64 <432000000>;
		};

		opp-576000000 {
			opp-hz = /bits/ 64 <576000000>;
		};
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};

	soc {
		deinterlace: deinterlace@1400000 {
			compatible = "allwinner,sun8i-h3-deinterlace";
			reg = <0x01400000 0x20000>;
			clocks = <&ccu CLK_BUS_DEINTERLACE>,
				 <&ccu CLK_DEINTERLACE>,
				 <&ccu CLK_DRAM_DEINTERLACE>;
			clock-names = "bus", "mod", "ram";
			resets = <&ccu RST_BUS_DEINTERLACE>;
			interrupts = <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>;
			interconnects = <&mbus 9>;
			interconnect-names = "dma-mem";
		};

		syscon: system-control@1c00000 {
			compatible = "allwinner,sun8i-h3-system-control";
			reg = <0x01c00000 0x1000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			sram_c: sram@1d00000 {
				compatible = "mmio-sram";
				reg = <0x01d00000 0x80000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x01d00000 0x80000>;

				ve_sram: sram-section@0 {
					compatible = "allwinner,sun8i-h3-sram-c1",
						     "allwinner,sun4i-a10-sram-c1";
					reg = <0x000000 0x80000>;
				};
			};
		};

		video-codec@1c0e000 {
			compatible = "allwinner,sun8i-h3-video-engine";
			reg = <0x01c0e000 0x1000>;
			clocks = <&ccu CLK_BUS_VE>, <&ccu CLK_VE>,
				 <&ccu CLK_DRAM_VE>;
			clock-names = "ahb", "mod", "ram";
			resets = <&ccu RST_BUS_VE>;
			interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>;
			allwinner,sram = <&ve_sram 1>;
		};

		crypto: crypto@1c15000 {
			compatible = "allwinner,sun8i-h3-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CE>, <&ccu CLK_CE>;
			clock-names = "bus", "mod";
			resets = <&ccu RST_BUS_CE>;
		};

		mali: gpu@1c40000 {
			compatible = "allwinner,sun8i-h3-mali", "arm,mali-400";
			reg = <0x01c40000 0x10000>;
			interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gp",
					  "gpmmu",
					  "pp0",
					  "ppmmu0",
					  "pp1",
					  "ppmmu1",
					  "pmu";
			clocks = <&ccu CLK_BUS_GPU>, <&ccu CLK_GPU>;
			clock-names = "bus", "core";
			resets = <&ccu RST_BUS_GPU>;
			operating-points-v2 = <&gpu_opp_table>;
		};

		ths: thermal-sensor@1c25000 {
			compatible = "allwinner,sun8i-h3-ths";
			reg = <0x01c25000 0x400>;
			interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&ccu RST_BUS_THS>;
			clocks = <&ccu CLK_BUS_THS>, <&ccu CLK_THS>;
			clock-names = "bus", "mod";
			nvmem-cells = <&ths_calibration>;
			nvmem-cell-names = "calibration";
			#thermal-sensor-cells = <0>;
		};
	};

	thermal-zones {
		cpu_thermal: cpu-thermal {
			polling-delay-passive = <0>;
			polling-delay = <0>;
			thermal-sensors = <&ths>;

			trips {
				cpu_hot_trip: cpu-hot {
					temperature = <80000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_very_hot_trip: cpu-very-hot {
					temperature = <100000>;
					hysteresis = <0>;
					type = "critical";
				};
			};

			cooling-maps {
				cpu-hot-limit {
					trip = <&cpu_hot_trip>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};
	};
};

&ccu {
	compatible = "allwinner,sun8i-h3-ccu";
};

&display_clocks {
	compatible = "allwinner,sun8i-h3-de2-clk";
};

&mbus {
	compatible = "allwinner,sun8i-h3-mbus";
};

&mmc0 {
	compatible = "allwinner,sun7i-a20-mmc";
	clocks = <&ccu CLK_BUS_MMC0>,
		 <&ccu CLK_MMC0>,
		 <&ccu CLK_MMC0_OUTPUT>,
		 <&ccu CLK_MMC0_SAMPLE>;
	clock-names = "ahb",
		      "mmc",
		      "output",
		      "sample";
};

&mmc1 {
	compatible = "allwinner,sun7i-a20-mmc";
	clocks = <&ccu CLK_BUS_MMC1>,
		 <&ccu CLK_MMC1>,
		 <&ccu CLK_MMC1_OUTPUT>,
		 <&ccu CLK_MMC1_SAMPLE>;
	clock-names = "ahb",
		      "mmc",
		      "output",
		      "sample";
};

&mmc2 {
	compatible = "allwinner,sun7i-a20-mmc";
	clocks = <&ccu CLK_BUS_MMC2>,
		 <&ccu CLK_MMC2>,
		 <&ccu CLK_MMC2_OUTPUT>,
		 <&ccu CLK_MMC2_SAMPLE>;
	clock-names = "ahb",
		      "mmc",
		      "output",
		      "sample";
};

&pio {
	compatible = "allwinner,sun8i-h3-pinctrl";
};

&rtc {
	compatible = "allwinner,sun8i-h3-rtc";
};

&sid {
	compatible = "allwinner,sun8i-h3-sid";
};
