/*
 * Copyright 2015 <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */
#include "sunxi-reference-design-tablet.dtsi"
#include "sun8i-reference-design-tablet.dtsi"

/ {
	aliases {
		serial0 = &r_uart;
		/* Make u-boot set mac-address for wifi without an eeprom */
		ethernet0 = &sdio_wifi;
	};

	panel: panel {
		/* Tablet dts should provide panel compatible */
		backlight = <&backlight>;
		enable-gpios = <&pio 7 7 GPIO_ACTIVE_HIGH>; /* PH7 */
		power-supply = <&reg_dc1sw>;

		port {
			panel_input: endpoint {
				remote-endpoint = <&tcon0_out_lcd>;
			};
		};
	};

	wifi_pwrseq: wifi_pwrseq {
		compatible = "mmc-pwrseq-simple";
		/*
		 * Q8 boards use various PL# pins as wifi-en. On other boards
		 * these may be connected to a wifi module output pin. To avoid
		 * short-circuits we configure these as inputs with pull-ups via
		 * pinctrl, instead of listing them as active-low reset-gpios.
		 */
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_pwrseq_pin_q8>;
		/* The esp8089 needs 200 ms after driving wifi-en high */
		post-power-on-delay-ms = <200>;
	};
};

&de {
	status = "okay";
};

&ehci0 {
	status  = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pg_pins>;
	vmmc-supply = <&reg_dldo1>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";

	sdio_wifi: sdio_wifi@1 {
		reg = <1>;
	};
};

&r_pio {
	wifi_pwrseq_pin_q8: wifi-pwrseq-pins {
		pins = "PL6", "PL7", "PL11";
		function = "gpio_in";
		bias-pull-up;
	};
};

&tcon0 {
	pinctrl-names = "default";
	pinctrl-0 = <&lcd_rgb666_pins>;
	status = "okay";
};

&usbphy {
	usb1_vbus-supply = <&reg_dldo1>;
};
