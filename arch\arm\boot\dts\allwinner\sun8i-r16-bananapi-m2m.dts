/*
 * Copyright (c) 2017 Free Electrons <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun8i-a33.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "BananaPi M2 Magic";
	compatible = "sinovoip,bananapi-m2m", "allwinner,sun8i-a33";

	aliases {
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		serial0 = &uart0;
		serial1 = &uart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "bpi-m2m:blue:usr";
			gpios = <&pio 2 7 GPIO_ACTIVE_LOW>;
		};

		led-1 {
			label = "bpi-m2m:green:usr";
			gpios = <&r_pio 0 2 GPIO_ACTIVE_LOW>;
		};

		led-2 {
			label = "bpi-m2m:red:power";
			gpios = <&r_pio 0 3 GPIO_ACTIVE_LOW>;
			default-state = "on";
		};
	};

	reg_vcc5v0: vcc5v0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	wifi_pwrseq: wifi_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&r_pio 0 6 GPIO_ACTIVE_LOW>; /* PL06 */
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "ext_clock";
	};
};

&codec {
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_dcdc3>;
};

&cpu0_opp_table {
	opp-1104000000 {
		opp-hz = /bits/ 64 <1104000000>;
		opp-microvolt = <1320000>;
		clock-latency-ns = <244144>; /* 8 32k periods */
	};

	opp-1200000000 {
		opp-hz = /bits/ 64 <1200000000>;
		opp-microvolt = <1320000>;
		clock-latency-ns = <244144>; /* 8 32k periods */
	};
};

&dai {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 1 4 GPIO_ACTIVE_LOW>; /* PB4 */
	status = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pg_pins>;
	vmmc-supply = <&reg_aldo1>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&r_rsb {
	status = "okay";

	axp22x: pmic@3a3 {
		compatible = "x-powers,axp223";
		reg = <0x3a3>;
		interrupt-parent = <&r_intc>;
		interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_LOW>;
		eldoin-supply = <&reg_dcdc1>;
		x-powers,drive-vbus-en;
	};
};

#include "axp223.dtsi"

&ac_power_supply {
	status = "okay";
};

&reg_aldo1 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "vcc-io";
};

&reg_aldo2 {
	regulator-always-on;
	regulator-min-microvolt = <2500000>;
	regulator-max-microvolt = <2500000>;
	regulator-name = "vdd-dll";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_dc1sw {
	regulator-name = "vcc-lcd";
};

&reg_dc5ldo {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpus";
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "vcc-3v0";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-sys";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <1400000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

/*
 * Our WiFi chip needs both DLDO1 and DLDO2 to be powered at the same
 * time, with the two being in sync. Since this is not really
 * supported right now, just use the two as always on, and we will fix
 * it later.
 */
&reg_dldo1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi0";
};

&reg_dldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi1";
};

&reg_drivevbus {
	regulator-name = "usb0-vbus";
	status = "okay";
};

&reg_rtc_ldo {
	regulator-name = "vcc-rtc";
};

&sound {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pg_pins>, <&uart1_cts_rts_pg_pins>;
	uart-has-rtscts;
	status = "okay";

	bluetooth {
		compatible = "brcm,bcm43438-bt";
		clocks = <&rtc CLK_OSC32K_FANOUT>;
		clock-names = "lpo";
		vbat-supply = <&reg_dldo1>;
		vddio-supply = <&reg_aldo3>;
		device-wakeup-gpios = <&r_pio 0 10 GPIO_ACTIVE_HIGH>; /* PL10 */
		host-wakeup-gpios = <&r_pio 0 9 GPIO_ACTIVE_HIGH>; /* PL9 */
		shutdown-gpios = <&r_pio 0 8 GPIO_ACTIVE_HIGH>; /* PL8 */
	};
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usb_power_supply {
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 7 8 GPIO_ACTIVE_HIGH>; /* PH8 */
	usb0_vbus_power-supply = <&usb_power_supply>;
	usb0_vbus-supply = <&reg_drivevbus>;
	usb1_vbus-supply = <&reg_vcc5v0>;
	status = "okay";
};
