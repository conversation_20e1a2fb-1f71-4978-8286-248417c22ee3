/{
	cpu0_opp_table: opp-table-cpu {
		compatible = "operating-points-v2";
		opp-shared;

		opp-720000000 {
			opp-hz = /bits/ 64 <720000000>;
			opp-microvolt = <1000000 1000000 1300000>;
			clock-latency-ns = <2000000>;
		};

		opp-912000000 {
			opp-hz = /bits/ 64 <912000000>;
			opp-microvolt = <1100000 1100000 1300000>;
			clock-latency-ns = <2000000>;
		};

		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <1160000 1160000 1300000>;
			clock-latency-ns = <2000000>;
		};

		opp-1104000000 {
			opp-hz = /bits/ 64 <1104000000>;
			opp-microvolt = <1240000 1240000 1300000>;
			clock-latency-ns = <2000000>;
		};

		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <1300000 1300000 1300000>;
			clock-latency-ns = <2000000>;
		};
	};
};

&cpu0 {
	operating-points-v2 = <&cpu0_opp_table>;
};

&cpu1 {
	operating-points-v2 = <&cpu0_opp_table>;
};

&cpu2 {
	operating-points-v2 = <&cpu0_opp_table>;
};

&cpu3 {
	operating-points-v2 = <&cpu0_opp_table>;
};
