// SPDX-License-Identifier: GPL-2.0+ OR MIT
// Copyright (C) 2021 <PERSON> <<EMAIL>>
// Based on the sun8i-r40-bananapi-m2-ultra.dts, which is:
//  Copyright (C) 2017 <PERSON><PERSON><PERSON> <<EMAIL>>
//  Copyright (C) 2017 <PERSON><PERSON><PERSON> <<EMAIL>>

#include "sun8i-r40.dtsi"
#include "sun8i-r40-cpu-opp.dtsi"

&cpu0 {
	cpu-supply = <&reg_dcdc2>;
};

&i2c0 {
	status = "okay";

	axp22x: pmic@34 {
		compatible = "x-powers,axp221";
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

#include "axp22x.dtsi"

&mmc2 {
	vmmc-supply = <&reg_dcdc1>;
	vqmmc-supply = <&reg_aldo2>;
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&pio {
	pinctrl-names = "default";
	pinctrl-0 = <&clk_out_a_pin>;
	vcc-pa-supply = <&reg_dcdc1>;
	vcc-pc-supply = <&reg_aldo2>;
	vcc-pd-supply = <&reg_dcdc1>;
	vcc-pf-supply = <&reg_dldo4>;
	vcc-pg-supply = <&reg_dldo1>;
};

&reg_aldo2 {
	regulator-always-on;
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-name = "vcc-pa";
};

&reg_aldo3 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&reg_dcdc1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-3v3";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1100000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <1100000>;
	regulator-max-microvolt = <1100000>;
	regulator-name = "vdd-sys";
};

&reg_dcdc5 {
	regulator-always-on;
	regulator-min-microvolt = <1500000>;
	regulator-max-microvolt = <1500000>;
	regulator-name = "vcc-dram";
};

&reg_dldo1 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi-io";
};

&reg_dldo4 {
	regulator-always-on;
	regulator-min-microvolt = <2500000>;
	regulator-max-microvolt = <2500000>;
	regulator-name = "vdd2v5-sata";
};

&reg_eldo2 {
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1200000>;
	regulator-name = "vdd1v2-sata";
};

&reg_eldo3 {
	regulator-min-microvolt = <2800000>;
	regulator-max-microvolt = <2800000>;
	regulator-name = "vcc-pe";
};
