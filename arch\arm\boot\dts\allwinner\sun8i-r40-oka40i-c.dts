// SPDX-License-Identifier: GPL-2.0+ OR MIT
// Copyright (C) 2021 Ivan <PERSON> <<EMAIL>>
// Based on the sun8i-r40-bananapi-m2-ultra.dts, which is:
//	Copyright (C) 2017 <PERSON><PERSON><PERSON> <<EMAIL>>
//	Copyright (C) 2017 <PERSON><PERSON><PERSON> <<EMAIL>>

/dts-v1/;
#include "sun8i-r40-feta40i.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>

/ {
	model = "Forlinx OKA40i-C";
	compatible = "forlinx,oka40i-c", "forlinx,feta40i-c", "allwinner,sun8i-r40";

	aliases {
		ethernet0 = &gmac;
		serial0 = &uart0;
		serial2 = &uart2;
		serial3 = &uart3;
		serial4 = &uart4;
		serial5 = &uart5; /* RS485 */
		serial7 = &uart7;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";

		led-5 { /* this is how the leds are labeled on the board */
			gpios = <&pio 7 26 GPIO_ACTIVE_LOW>; /* PH26 */
			color = <LED_COLOR_ID_GREEN>;
			function = LED_FUNCTION_STATUS;
		};

		led-6 {
			gpios = <&pio 8 15 GPIO_ACTIVE_LOW>; /* PI15 */
			color = <LED_COLOR_ID_BLUE>;
			function = LED_FUNCTION_STATUS;
		};
	};

	reg_vcc5v0: vcc5v0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	wifi_pwrseq: wifi_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 1 10 GPIO_ACTIVE_LOW>; // PB10 WIFI_EN
		clocks = <&ccu CLK_OUTA>;
		clock-names = "ext_clock";
	};
};

&ahci {
	ahci-supply = <&reg_dldo4>;
	phy-supply = <&reg_eldo2>;
	status = "okay";
};

&de {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ehci2 {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "rgmii-id";
	phy-supply = <&reg_dcdc1>;
	status = "okay";
};

&gmac_mdio {
	phy1: ethernet-phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <1>;
	};
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&i2c2 {
	status = "okay";
};

&mmc0 {
	vmmc-supply = <&reg_dcdc1>;
	vqmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 8 11 GPIO_ACTIVE_LOW>; // PI11
	status = "okay";
};

&mmc3 {
	vmmc-supply = <&reg_dcdc1>;
	vqmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 8 10 GPIO_ACTIVE_LOW>; // PI10
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&ohci2 {
	status = "okay";
};

&reg_dc1sw {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-lcd";
};

&reg_dldo2 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vcc-wifi";
};

&tcon_tv0 {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pb_pins>;
	status = "okay";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart2_pi_pins>, <&uart2_rts_cts_pi_pins>;
	uart-has-rtscts;
	status = "okay";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pg_pins>, <&uart3_rts_cts_pg_pins>;
	uart-has-rtscts;
	status = "okay";
};

&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart4_pg_pins>;
	status = "okay";
};

&uart5 { /* RS485 */
	pinctrl-names = "default";
	pinctrl-0 = <&uart5_ph_pins>;
	status = "okay";
};

&uart7 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart7_pi_pins>;
	status = "okay";
};

&usbphy {
	usb1_vbus-supply = <&reg_vcc5v0>;
	usb2_vbus-supply = <&reg_vcc5v0>;
	status = "okay";
};
