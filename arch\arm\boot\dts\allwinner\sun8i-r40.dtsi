/*
 * Copyright 2017 <PERSON><PERSON><PERSON> <<EMAIL>>
 * Copyright 2017 <PERSON><PERSON>wy <PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/sun6i-rtc.h>
#include <dt-bindings/clock/sun8i-de2.h>
#include <dt-bindings/clock/sun8i-r40-ccu.h>
#include <dt-bindings/clock/sun8i-tcon-top.h>
#include <dt-bindings/reset/sun8i-r40-ccu.h>
#include <dt-bindings/reset/sun8i-de2.h>
#include <dt-bindings/thermal/thermal.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: osc24M {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-accuracy = <50000>;
			clock-output-names = "osc24M";
		};

		osc32k: osc32k {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-accuracy = <20000>;
			clock-output-names = "ext-osc32k";
		};
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clocks = <&ccu CLK_CPU>;
			clock-names = "cpu";
			#cooling-cells = <2>;
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <1>;
			clocks = <&ccu CLK_CPU>;
			clock-names = "cpu";
			#cooling-cells = <2>;
		};

		cpu2: cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <2>;
			clocks = <&ccu CLK_CPU>;
			clock-names = "cpu";
			#cooling-cells = <2>;
		};

		cpu3: cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <3>;
			clocks = <&ccu CLK_CPU>;
			clock-names = "cpu";
			#cooling-cells = <2>;
		};
	};

	de: display-engine {
		compatible = "allwinner,sun8i-r40-display-engine";
		allwinner,pipelines = <&mixer0>, <&mixer1>;
		status = "disabled";
	};

	thermal-zones {
		cpu_thermal: cpu0-thermal {
			/* milliseconds */
			polling-delay-passive = <0>;
			polling-delay = <0>;
			thermal-sensors = <&ths 0>;

			trips {
				cpu_hot_trip: cpu-hot {
					temperature = <80000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_very_hot_trip: cpu-very-hot {
					temperature = <115000>;
					hysteresis = <0>;
					type = "critical";
				};
			};

			cooling-maps {
				cpu-hot-limit {
					trip = <&cpu_hot_trip>;
					cooling-device = <&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
							 <&cpu3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};

		gpu_thermal: gpu-thermal {
			/* milliseconds */
			polling-delay-passive = <0>;
			polling-delay = <0>;
			thermal-sensors = <&ths 1>;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		display_clocks: clock@1000000 {
			compatible = "allwinner,sun8i-r40-de2-clk",
				     "allwinner,sun8i-h3-de2-clk";
			reg = <0x01000000 0x10000>;
			clocks = <&ccu CLK_BUS_DE>,
				 <&ccu CLK_DE>;
			clock-names = "bus",
				      "mod";
			resets = <&ccu RST_BUS_DE>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		mixer0: mixer@1100000 {
			compatible = "allwinner,sun8i-r40-de2-mixer-0";
			reg = <0x01100000 0x100000>;
			clocks = <&display_clocks CLK_BUS_MIXER0>,
				 <&display_clocks CLK_MIXER0>;
			clock-names = "bus",
				      "mod";
			resets = <&display_clocks RST_MIXER0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				mixer0_out: port@1 {
					reg = <1>;
					mixer0_out_tcon_top: endpoint {
						remote-endpoint = <&tcon_top_mixer0_in_mixer0>;
					};
				};
			};
		};

		mixer1: mixer@1200000 {
			compatible = "allwinner,sun8i-r40-de2-mixer-1";
			reg = <0x01200000 0x100000>;
			clocks = <&display_clocks CLK_BUS_MIXER1>,
				 <&display_clocks CLK_MIXER1>;
			clock-names = "bus",
				      "mod";
			resets = <&display_clocks RST_WB>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				mixer1_out: port@1 {
					reg = <1>;
					mixer1_out_tcon_top: endpoint {
						remote-endpoint = <&tcon_top_mixer1_in_mixer1>;
					};
				};
			};
		};

		deinterlace: deinterlace@1400000 {
			compatible = "allwinner,sun8i-r40-deinterlace",
				     "allwinner,sun8i-h3-deinterlace";
			reg = <0x01400000 0x20000>;
			clocks = <&ccu CLK_BUS_DEINTERLACE>,
				 <&ccu CLK_DEINTERLACE>,
				 /*
				  * NOTE: Contrary to what datasheet claims,
				  * DRAM deinterlace gate doesn't exist and
				  * it's shared with CSI1.
				  */
				 <&ccu CLK_DRAM_CSI1>;
			clock-names = "bus", "mod", "ram";
			resets = <&ccu RST_BUS_DEINTERLACE>;
			interrupts = <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>;
			interconnects = <&mbus 9>;
			interconnect-names = "dma-mem";
		};

		syscon: system-control@1c00000 {
			compatible = "allwinner,sun8i-r40-system-control",
				     "allwinner,sun4i-a10-system-control";
			reg = <0x01c00000 0x30>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			sram_c: sram@1d00000 {
				compatible = "mmio-sram";
				reg = <0x01d00000 0xd0000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges = <0 0x01d00000 0xd0000>;

				ve_sram: sram-section@0 {
					compatible = "allwinner,sun8i-r40-sram-c1",
						     "allwinner,sun4i-a10-sram-c1";
					reg = <0x000000 0x80000>;
				};
			};
		};

		nmi_intc: interrupt-controller@1c00030 {
			compatible = "allwinner,sun7i-a20-sc-nmi";
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x01c00030 0x0c>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun8i-r40-dma",
				     "allwinner,sun50i-a64-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DMA>;
			dma-channels = <16>;
			dma-requests = <31>;
			resets = <&ccu RST_BUS_DMA>;
			#dma-cells = <1>;
		};

		spi0: spi@1c05000 {
			compatible = "allwinner,sun8i-r40-spi",
				     "allwinner,sun8i-h3-spi";
			reg = <0x01c05000 0x1000>;
			interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPI0>, <&ccu CLK_SPI0>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_SPI0>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi1: spi@1c06000 {
			compatible = "allwinner,sun8i-r40-spi",
				     "allwinner,sun8i-h3-spi";
			reg = <0x01c06000 0x1000>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPI1>, <&ccu CLK_SPI1>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_SPI1>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		csi0: csi@1c09000 {
			compatible = "allwinner,sun8i-r40-csi0",
				     "allwinner,sun7i-a20-csi0";
			reg = <0x01c09000 0x1000>;
			interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CSI0>, <&ccu CLK_CSI_SCLK>,
				 <&ccu CLK_DRAM_CSI0>;
			clock-names = "bus", "isp", "ram";
			resets = <&ccu RST_BUS_CSI0>;
			interconnects = <&mbus 5>;
			interconnect-names = "dma-mem";
			status = "disabled";
		};

		video-codec@1c0e000 {
			compatible = "allwinner,sun8i-r40-video-engine";
			reg = <0x01c0e000 0x1000>;
			clocks = <&ccu CLK_BUS_VE>, <&ccu CLK_VE>,
			<&ccu CLK_DRAM_VE>;
			clock-names = "ahb", "mod", "ram";
			resets = <&ccu RST_BUS_VE>;
			interrupts = <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>;
			allwinner,sram = <&ve_sram 1>;
		};

		mmc0: mmc@1c0f000 {
			compatible = "allwinner,sun8i-r40-mmc",
				     "allwinner,sun50i-a64-mmc";
			reg = <0x01c0f000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC0>, <&ccu CLK_MMC0>;
			clock-names = "ahb", "mmc";
			resets = <&ccu RST_BUS_MMC0>;
			reset-names = "ahb";
			pinctrl-0 = <&mmc0_pins>;
			pinctrl-names = "default";
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			compatible = "allwinner,sun8i-r40-mmc",
				     "allwinner,sun50i-a64-mmc";
			reg = <0x01c10000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC1>, <&ccu CLK_MMC1>;
			clock-names = "ahb", "mmc";
			resets = <&ccu RST_BUS_MMC1>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			compatible = "allwinner,sun8i-r40-emmc",
				     "allwinner,sun50i-a64-emmc";
			reg = <0x01c11000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC2>, <&ccu CLK_MMC2>;
			clock-names = "ahb", "mmc";
			resets = <&ccu RST_BUS_MMC2>;
			reset-names = "ahb";
			pinctrl-0 = <&mmc2_pins>;
			pinctrl-names = "default";
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc3: mmc@1c12000 {
			compatible = "allwinner,sun8i-r40-mmc",
				     "allwinner,sun50i-a64-mmc";
			reg = <0x01c12000 0x1000>;
			clocks = <&ccu CLK_BUS_MMC3>, <&ccu CLK_MMC3>;
			clock-names = "ahb", "mmc";
			resets = <&ccu RST_BUS_MMC3>;
			reset-names = "ahb";
			pinctrl-0 = <&mmc3_pins>;
			pinctrl-names = "default";
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		usbphy: phy@1c13400 {
			compatible = "allwinner,sun8i-r40-usb-phy";
			reg = <0x01c13400 0x14>,
			      <0x01c14800 0x4>,
			      <0x01c19800 0x4>,
			      <0x01c1c800 0x4>;
			reg-names = "phy_ctrl",
				    "pmu0",
				    "pmu1",
				    "pmu2";
			clocks = <&ccu CLK_USB_PHY0>,
				 <&ccu CLK_USB_PHY1>,
				 <&ccu CLK_USB_PHY2>;
			clock-names = "usb0_phy",
				      "usb1_phy",
				      "usb2_phy";
			resets = <&ccu RST_USB_PHY0>,
				 <&ccu RST_USB_PHY1>,
				 <&ccu RST_USB_PHY2>;
			reset-names = "usb0_reset",
				      "usb1_reset",
				      "usb2_reset";
			status = "disabled";
			#phy-cells = <1>;
		};

		crypto: crypto@1c15000 {
			compatible = "allwinner,sun8i-r40-crypto";
			reg = <0x01c15000 0x1000>;
			interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CE>, <&ccu CLK_CE>;
			clock-names = "bus", "mod";
			resets = <&ccu RST_BUS_CE>;
		};

		spi2: spi@1c17000 {
			compatible = "allwinner,sun8i-r40-spi",
				     "allwinner,sun8i-h3-spi";
			reg = <0x01c17000 0x1000>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPI2>, <&ccu CLK_SPI2>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_SPI2>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		ahci: sata@1c18000 {
			compatible = "allwinner,sun8i-r40-ahci";
			reg = <0x01c18000 0x1000>;
			interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SATA>, <&ccu CLK_SATA>;
			resets = <&ccu RST_BUS_SATA>;
			reset-names = "ahci";
			status = "disabled";
		};

		ehci1: usb@1c19000 {
			compatible = "allwinner,sun8i-r40-ehci", "generic-ehci";
			reg = <0x01c19000 0x100>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI1>;
			resets = <&ccu RST_BUS_EHCI1>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci1: usb@1c19400 {
			compatible = "allwinner,sun8i-r40-ohci", "generic-ohci";
			reg = <0x01c19400 0x100>;
			interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_OHCI1>,
				 <&ccu CLK_USB_OHCI1>;
			resets = <&ccu RST_BUS_OHCI1>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ehci2: usb@1c1c000 {
			compatible = "allwinner,sun8i-r40-ehci", "generic-ehci";
			reg = <0x01c1c000 0x100>;
			interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI2>;
			resets = <&ccu RST_BUS_EHCI2>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci2: usb@1c1c400 {
			compatible = "allwinner,sun8i-r40-ohci", "generic-ohci";
			reg = <0x01c1c400 0x100>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_OHCI2>,
				 <&ccu CLK_USB_OHCI2>;
			resets = <&ccu RST_BUS_OHCI2>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		spi3: spi@1c1f000 {
			compatible = "allwinner,sun8i-r40-spi",
				     "allwinner,sun8i-h3-spi";
			reg = <0x01c1f000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPI3>, <&ccu CLK_SPI3>;
			clock-names = "ahb", "mod";
			resets = <&ccu RST_BUS_SPI3>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		ccu: clock@1c20000 {
			compatible = "allwinner,sun8i-r40-ccu";
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&rtc CLK_OSC32K>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		rtc: rtc@1c20400 {
			compatible = "allwinner,sun8i-r40-rtc";
			reg = <0x01c20400 0x400>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			clock-output-names = "osc32k", "osc32k-out";
			clocks = <&osc32k>;
			#clock-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			compatible = "allwinner,sun8i-r40-pinctrl";
			reg = <0x01c20800 0x400>;
			interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_PIO>, <&osc24M>,
				 <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			interrupt-controller;
			#interrupt-cells = <3>;
			#gpio-cells = <3>;

			can_ph_pins: can-ph-pins {
				pins = "PH20", "PH21";
				function = "can";
			};

			can_pa_pins: can-pa-pins {
				pins = "PA16", "PA17";
				function = "can";
			};

			clk_out_a_pin: clk-out-a-pin {
				pins = "PI12";
				function = "clk_out_a";
			};

			/omit-if-no-ref/
			csi0_8bits_pins: csi0-8bits-pins {
				pins = "PE0", "PE2", "PE3", "PE4", "PE5",
				       "PE6", "PE7", "PE8", "PE9", "PE10",
				       "PE11";
				function = "csi0";
			};

			/omit-if-no-ref/
			csi0_mclk_pin: csi0-mclk-pin {
				pins = "PE1";
				function = "csi0";
			};

			gmac_rgmii_pins: gmac-rgmii-pins {
				pins = "PA0", "PA1", "PA2", "PA3",
				       "PA4", "PA5", "PA6", "PA7",
				       "PA8", "PA10", "PA11", "PA12",
				       "PA13", "PA15", "PA16";
				function = "gmac";
				/*
				 * data lines in RGMII mode use DDR mode
				 * and need a higher signal drive strength
				 */
				drive-strength = <40>;
			};

			i2c0_pins: i2c0-pins {
				pins = "PB0", "PB1";
				function = "i2c0";
			};

			i2c1_pins: i2c1-pins {
				pins = "PB18", "PB19";
				function = "i2c1";
			};

			i2c2_pins: i2c2-pins {
				pins = "PB20", "PB21";
				function = "i2c2";
			};

			i2c3_pins: i2c3-pins {
				pins = "PI0", "PI1";
				function = "i2c3";
			};

			i2c4_pins: i2c4-pins {
				pins = "PI2", "PI3";
				function = "i2c4";
			};

			ir0_pins: ir0-pins {
				pins = "PB4";
				function = "ir0";
			};

			ir1_pins: ir1-pins {
				pins = "PB23";
				function = "ir1";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2",
				       "PF3", "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc1_pg_pins: mmc1-pg-pins {
				pins = "PG0", "PG1", "PG2",
				       "PG3", "PG4", "PG5";
				function = "mmc1";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_pins: mmc2-pins {
				pins = "PC5", "PC6", "PC7", "PC8", "PC9",
				       "PC10", "PC11", "PC12", "PC13", "PC14",
				       "PC15", "PC24";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			/omit-if-no-ref/
			mmc3_pins: mmc3-pins {
				pins = "PI4", "PI5", "PI6",
				       "PI7", "PI8", "PI9";
				function = "mmc3";
				drive-strength = <30>;
				bias-pull-up;
			};

			/omit-if-no-ref/
			spi0_pc_pins: spi0-pc-pins {
				pins = "PC0", "PC1", "PC2";
				function = "spi0";
			};

			/omit-if-no-ref/
			spi0_cs0_pc_pin: spi0-cs0-pc-pin {
				pins = "PC23";
				function = "spi0";
			};

			/omit-if-no-ref/
			spi1_pi_pins: spi1-pi-pins {
				pins = "PI17", "PI18", "PI19";
				function = "spi1";
			};

			/omit-if-no-ref/
			spi1_cs0_pi_pin: spi1-cs0-pi-pin {
				pins = "PI16";
				function = "spi1";
			};

			/omit-if-no-ref/
			spi1_cs1_pi_pin: spi1-cs1-pi-pin {
				pins = "PI15";
				function = "spi1";
			};

			/omit-if-no-ref/
			uart0_pb_pins: uart0-pb-pins {
				pins = "PB22", "PB23";
				function = "uart0";
			};

			/omit-if-no-ref/
			uart2_pi_pins: uart2-pi-pins {
				pins = "PI18", "PI19";
				function = "uart2";
			};

			/omit-if-no-ref/
			uart2_rts_cts_pi_pins: uart2-rts-cts-pi-pins{
				pins = "PI16", "PI17";
				function = "uart2";
			};

			/omit-if-no-ref/
			uart3_pg_pins: uart3-pg-pins {
				pins = "PG6", "PG7";
				function = "uart3";
			};

			/omit-if-no-ref/
			uart3_rts_cts_pg_pins: uart3-rts-cts-pg-pins {
				pins = "PG8", "PG9";
				function = "uart3";
			};

			/omit-if-no-ref/
			uart4_pg_pins: uart4-pg-pins {
				pins = "PG10", "PG11";
				function = "uart4";
			};

			/omit-if-no-ref/
			uart5_ph_pins: uart5-ph-pins {
				pins = "PH6", "PH7";
				function = "uart5";
			};

			/omit-if-no-ref/
			uart7_pi_pins: uart7-pi-pins {
				pins = "PI20", "PI21";
				function = "uart7";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun4i-a10-timer";
			reg = <0x01c20c00 0x90>;
			interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		wdt: watchdog@1c20c90 {
			compatible = "allwinner,sun4i-a10-wdt";
			reg = <0x01c20c90 0x10>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		ir0: ir@1c21800 {
			compatible = "allwinner,sun8i-r40-ir",
				     "allwinner,sun6i-a31-ir";
			reg = <0x01c21800 0x400>;
			pinctrl-0 = <&ir0_pins>;
			pinctrl-names = "default";
			clocks = <&ccu CLK_BUS_IR0>, <&ccu CLK_IR0>;
			clock-names = "apb", "ir";
			interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&ccu RST_BUS_IR0>;
			status = "disabled";
		};

		ir1: ir@1c21c00 {
			compatible = "allwinner,sun8i-r40-ir",
				     "allwinner,sun6i-a31-ir";
			reg = <0x01c21c00 0x400>;
			pinctrl-0 = <&ir1_pins>;
			pinctrl-names = "default";
			clocks = <&ccu CLK_BUS_IR1>, <&ccu CLK_IR1>;
			clock-names = "apb", "ir";
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&ccu RST_BUS_IR1>;
			status = "disabled";
		};

		i2s0: i2s@1c22000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-r40-i2s",
				     "allwinner,sun8i-h3-i2s";
			reg = <0x01c22000 0x400>;
			interrupts = <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S0>, <&ccu CLK_I2S0>;
			clock-names = "apb", "mod";
			resets = <&ccu RST_BUS_I2S0>;
			dmas = <&dma 3>, <&dma 3>;
			dma-names = "rx", "tx";
		};

		i2s1: i2s@1c22400 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-r40-i2s",
				     "allwinner,sun8i-h3-i2s";
			reg = <0x01c22400 0x400>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S1>, <&ccu CLK_I2S1>;
			clock-names = "apb", "mod";
			resets = <&ccu RST_BUS_I2S1>;
			dmas = <&dma 4>, <&dma 4>;
			dma-names = "rx", "tx";
		};

		i2s2: i2s@1c22800 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-r40-i2s",
				     "allwinner,sun8i-h3-i2s";
			reg = <0x01c22800 0x400>;
			interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S2>, <&ccu CLK_I2S2>;
			clock-names = "apb", "mod";
			resets = <&ccu RST_BUS_I2S2>;
			dmas = <&dma 6>, <&dma 6>;
			dma-names = "rx", "tx";
		};

		ths: thermal-sensor@1c24c00 {
			compatible = "allwinner,sun8i-r40-ths";
			reg = <0x01c24c00 0x100>;
			clocks = <&ccu CLK_BUS_THS>, <&ccu CLK_THS>;
			clock-names = "bus", "mod";
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&ccu RST_BUS_THS>;
			/* TODO: add nvmem-cells for calibration */
			#thermal-sensor-cells = <1>;
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART0>;
			resets = <&ccu RST_BUS_UART0>;
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART1>;
			resets = <&ccu RST_BUS_UART1>;
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART2>;
			resets = <&ccu RST_BUS_UART2>;
			status = "disabled";
		};

		uart3: serial@1c28c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28c00 0x400>;
			interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART3>;
			resets = <&ccu RST_BUS_UART3>;
			status = "disabled";
		};

		uart4: serial@1c29000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29000 0x400>;
			interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART4>;
			resets = <&ccu RST_BUS_UART4>;
			status = "disabled";
		};

		uart5: serial@1c29400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29400 0x400>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART5>;
			resets = <&ccu RST_BUS_UART5>;
			status = "disabled";
		};

		uart6: serial@1c29800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29800 0x400>;
			interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART6>;
			resets = <&ccu RST_BUS_UART6>;
			status = "disabled";
		};

		uart7: serial@1c29c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c29c00 0x400>;
			interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART7>;
			resets = <&ccu RST_BUS_UART7>;
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C0>;
			resets = <&ccu RST_BUS_I2C0>;
			pinctrl-0 = <&i2c0_pins>;
			pinctrl-names = "default";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C1>;
			resets = <&ccu RST_BUS_I2C1>;
			pinctrl-0 = <&i2c1_pins>;
			pinctrl-names = "default";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@1c2b400 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b400 0x400>;
			interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C2>;
			resets = <&ccu RST_BUS_I2C2>;
			pinctrl-0 = <&i2c2_pins>;
			pinctrl-names = "default";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c3: i2c@1c2b800 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b800 0x400>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C3>;
			resets = <&ccu RST_BUS_I2C3>;
			pinctrl-0 = <&i2c3_pins>;
			pinctrl-names = "default";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		can0: can@1c2bc00 {
			compatible = "allwinner,sun8i-r40-can";
			reg = <0x01c2bc00 0x400>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CAN>;
			resets = <&ccu RST_BUS_CAN>;
			status = "disabled";
		};

		i2c4: i2c@1c2c000 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2c000 0x400>;
			interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C4>;
			resets = <&ccu RST_BUS_I2C4>;
			pinctrl-0 = <&i2c4_pins>;
			pinctrl-names = "default";
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mali: gpu@1c40000 {
			compatible = "allwinner,sun8i-r40-mali", "arm,mali-400";
			reg = <0x01c40000 0x10000>;
			interrupts = <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gp",
					  "gpmmu",
					  "pp0",
					  "ppmmu0",
					  "pp1",
					  "ppmmu1",
					  "pmu";
			clocks = <&ccu CLK_BUS_GPU>, <&ccu CLK_GPU>;
			clock-names = "bus", "core";
			resets = <&ccu RST_BUS_GPU>;
		};

		gmac: ethernet@1c50000 {
			compatible = "allwinner,sun8i-r40-gmac";
			syscon = <&ccu>;
			reg = <0x01c50000 0x10000>;
			interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq";
			resets = <&ccu RST_BUS_GMAC>;
			reset-names = "stmmaceth";
			clocks = <&ccu CLK_BUS_GMAC>;
			clock-names = "stmmaceth";
			status = "disabled";

			gmac_mdio: mdio {
				compatible = "snps,dwmac-mdio";
				#address-cells = <1>;
				#size-cells = <0>;
			};
		};

		mbus: dram-controller@1c62000 {
			compatible = "allwinner,sun8i-r40-mbus";
			reg = <0x01c62000 0x1000>;
			clocks = <&ccu 155>;
			#address-cells = <1>;
			#size-cells = <1>;
			dma-ranges = <0x00000000 0x40000000 0x80000000>;
			#interconnect-cells = <1>;
		};

		tcon_top: tcon-top@1c70000 {
			compatible = "allwinner,sun8i-r40-tcon-top";
			reg = <0x01c70000 0x1000>;
			clocks = <&ccu CLK_BUS_TCON_TOP>,
				 <&ccu CLK_TCON_TV0>,
				 <&ccu CLK_TVE0>,
				 <&ccu CLK_TCON_TV1>,
				 <&ccu CLK_TVE1>,
				 <&ccu CLK_DSI_DPHY>;
			clock-names = "bus",
				      "tcon-tv0",
				      "tve0",
				      "tcon-tv1",
				      "tve1",
				      "dsi";
			clock-output-names = "tcon-top-tv0",
					     "tcon-top-tv1",
					     "tcon-top-dsi";
			resets = <&ccu RST_BUS_TCON_TOP>;
			#clock-cells = <1>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon_top_mixer0_in: port@0 {
					reg = <0>;

					tcon_top_mixer0_in_mixer0: endpoint {
						remote-endpoint = <&mixer0_out_tcon_top>;
					};
				};

				tcon_top_mixer0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon_top_mixer0_out_tcon_lcd0: endpoint@0 {
						reg = <0>;
					};

					tcon_top_mixer0_out_tcon_lcd1: endpoint@1 {
						reg = <1>;
					};

					tcon_top_mixer0_out_tcon_tv0: endpoint@2 {
						reg = <2>;
						remote-endpoint = <&tcon_tv0_in_tcon_top_mixer0>;
					};

					tcon_top_mixer0_out_tcon_tv1: endpoint@3 {
						reg = <3>;
						remote-endpoint = <&tcon_tv1_in_tcon_top_mixer0>;
					};
				};

				tcon_top_mixer1_in: port@2 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <2>;

					tcon_top_mixer1_in_mixer1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&mixer1_out_tcon_top>;
					};
				};

				tcon_top_mixer1_out: port@3 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <3>;

					tcon_top_mixer1_out_tcon_lcd0: endpoint@0 {
						reg = <0>;
					};

					tcon_top_mixer1_out_tcon_lcd1: endpoint@1 {
						reg = <1>;
					};

					tcon_top_mixer1_out_tcon_tv0: endpoint@2 {
						reg = <2>;
						remote-endpoint = <&tcon_tv0_in_tcon_top_mixer1>;
					};

					tcon_top_mixer1_out_tcon_tv1: endpoint@3 {
						reg = <3>;
						remote-endpoint = <&tcon_tv1_in_tcon_top_mixer1>;
					};
				};

				tcon_top_hdmi_in: port@4 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <4>;

					tcon_top_hdmi_in_tcon_tv0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon_tv0_out_tcon_top>;
					};

					tcon_top_hdmi_in_tcon_tv1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon_tv1_out_tcon_top>;
					};
				};

				tcon_top_hdmi_out: port@5 {
					reg = <5>;

					tcon_top_hdmi_out_hdmi: endpoint {
						remote-endpoint = <&hdmi_in_tcon_top>;
					};
				};
			};
		};

		tcon_tv0: lcd-controller@1c73000 {
			compatible = "allwinner,sun8i-r40-tcon-tv";
			reg = <0x01c73000 0x1000>;
			interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_TCON_TV0>, <&tcon_top CLK_TCON_TOP_TV0>;
			clock-names = "ahb", "tcon-ch1";
			resets = <&ccu RST_BUS_TCON_TV0>;
			reset-names = "lcd";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon_tv0_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon_tv0_in_tcon_top_mixer0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon_top_mixer0_out_tcon_tv0>;
					};

					tcon_tv0_in_tcon_top_mixer1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon_top_mixer1_out_tcon_tv0>;
					};
				};

				tcon_tv0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon_tv0_out_tcon_top: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon_top_hdmi_in_tcon_tv0>;
					};
				};
			};
		};

		tcon_tv1: lcd-controller@1c74000 {
			compatible = "allwinner,sun8i-r40-tcon-tv";
			reg = <0x01c74000 0x1000>;
			interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_TCON_TV1>, <&tcon_top CLK_TCON_TOP_TV1>;
			clock-names = "ahb", "tcon-ch1";
			resets = <&ccu RST_BUS_TCON_TV1>;
			reset-names = "lcd";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon_tv1_in: port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					tcon_tv1_in_tcon_top_mixer0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&tcon_top_mixer0_out_tcon_tv1>;
					};

					tcon_tv1_in_tcon_top_mixer1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon_top_mixer1_out_tcon_tv1>;
					};
				};

				tcon_tv1_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon_tv1_out_tcon_top: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&tcon_top_hdmi_in_tcon_tv1>;
					};
				};
			};
		};

		gic: interrupt-controller@1c81000 {
			compatible = "arm,gic-400";
			reg = <0x01c81000 0x1000>,
			      <0x01c82000 0x2000>,
			      <0x01c84000 0x2000>,
			      <0x01c86000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		hdmi: hdmi@1ee0000 {
			compatible = "allwinner,sun8i-r40-dw-hdmi",
				     "allwinner,sun8i-a83t-dw-hdmi";
			reg = <0x01ee0000 0x10000>;
			reg-io-width = <1>;
			interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_HDMI0>, <&ccu CLK_HDMI_SLOW>,
				 <&ccu CLK_HDMI>, <&rtc CLK_OSC32K>;
			clock-names = "iahb", "isfr", "tmds", "cec";
			resets = <&ccu RST_BUS_HDMI1>;
			reset-names = "ctrl";
			phys = <&hdmi_phy>;
			phy-names = "phy";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi_in: port@0 {
					reg = <0>;

					hdmi_in_tcon_top: endpoint {
						remote-endpoint = <&tcon_top_hdmi_out_hdmi>;
					};
				};

				hdmi_out: port@1 {
					reg = <1>;
				};
			};
		};

		hdmi_phy: hdmi-phy@1ef0000 {
			compatible = "allwinner,sun8i-r40-hdmi-phy";
			reg = <0x01ef0000 0x10000>;
			clocks = <&ccu CLK_BUS_HDMI1>, <&ccu CLK_HDMI_SLOW>,
				 <&ccu CLK_PLL_VIDEO0>, <&ccu CLK_PLL_VIDEO1>;
			clock-names = "bus", "mod", "pll-0", "pll-1";
			resets = <&ccu RST_BUS_HDMI0>;
			reset-names = "phy";
			#phy-cells = <0>;
		};
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};
};
