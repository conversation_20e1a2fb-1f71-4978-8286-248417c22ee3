// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2020 <PERSON> <<EMAIL>>
 */

/dts-v1/;
#include "sun8i-v3.dtsi"
#include "sunxi-common-regulators.dtsi"

/ {
	model = "Elimo Impetus SoM";
	compatible = "elimo,impetus", "sochip,s3", "allwinner,sun8i-v3";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&mmc0 {
	broken-cd;
	bus-width = <4>;
	vmmc-supply = <&reg_vcc3v3>;
	status = "okay";
};

&uart0 {
	pinctrl-0 = <&uart0_pb_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&usb_otg {
	dr_mode = "otg";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 5 6 GPIO_ACTIVE_HIGH>;
	status = "okay";
};
