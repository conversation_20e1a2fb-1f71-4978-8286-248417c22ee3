// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2019 Icenowy Zheng <<EMAIL>>
 */

/dts-v1/;
#include "sun8i-v3.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Sipeed Lichee Zero Plus";
	compatible = "sipeed,lichee-zero-plus", "sochip,s3",
		     "allwinner,sun8i-v3";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	reg_vcc3v3: vcc3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&mmc0 {
	broken-cd;
	bus-width = <4>;
	vmmc-supply = <&reg_vcc3v3>;
	status = "okay";
};

&uart0 {
	pinctrl-0 = <&uart0_pb_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	usb0_id_det-gpios = <&pio 5 6 GPIO_ACTIVE_HIGH>;
	status = "okay";
};
