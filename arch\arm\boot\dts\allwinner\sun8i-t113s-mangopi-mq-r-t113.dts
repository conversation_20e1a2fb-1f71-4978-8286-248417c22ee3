// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
// Copyright (C) 2022 Arm Ltd.

#include <dt-bindings/interrupt-controller/irq.h>

/dts-v1/;

#include "sun8i-t113s.dtsi"
#include "sunxi-d1s-t113-mangopi-mq-r.dtsi"

/ {
	model = "MangoPi MQ-R-T113";
	compatible = "widora,mangopi-mq-r-t113", "allwinner,sun8i-t113s";

	aliases {
		ethernet0 = &rtl8189ftv;
	};
};

&cpu0 {
	cpu-supply = <&reg_vcc_core>;
};

&cpu1 {
	cpu-supply = <&reg_vcc_core>;
};

&mmc1 {
	rtl8189ftv: wifi@1 {
		reg = <1>;
		interrupt-parent = <&pio>;
		interrupts = <6 10 IRQ_TYPE_LEVEL_LOW>; /* PG10 = WL_WAKE_AP */
		interrupt-names = "host-wake";
	};
};
