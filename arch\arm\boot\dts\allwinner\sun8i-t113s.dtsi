// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
// Copyright (C) 2022 Arm Ltd.

#define SOC_PERIPHERAL_IRQ(nr) GIC_SPI nr

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <riscv/allwinner/sunxi-d1s-t113.dtsi>
#include <riscv/allwinner/sunxi-d1-t113.dtsi>

/ {
	interrupt-parent = <&gic>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <1>;
			clocks = <&ccu CLK_CPUX>;
			clock-names = "cpu";
		};
	};

	gic: interrupt-controller@1c81000 {
		compatible = "arm,gic-400";
		reg = <0x03021000 0x1000>,
		      <0x03022000 0x2000>,
		      <0x03024000 0x2000>,
		      <0x03026000 0x2000>;
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		interrupt-controller;
		#interrupt-cells = <3>;
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 172 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 173 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>;
	};
};
