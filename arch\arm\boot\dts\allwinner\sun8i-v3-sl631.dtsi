// SPDX-License-Identifier: (GPL-2.0+ OR X11)
/*
 * Copyright 2020 <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "sun8i-v3.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	model = "SL631 Action Camera";
	compatible = "allwinner,sl631", "allwinner,sun8i-v3";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};
};

&i2c0 {
	status = "okay";

	axp209: pmic@34 {
		reg = <0x34>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
	};
};

&i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1_pb_pins>;
	status = "okay";
};

&lradc {
	vref-supply = <&reg_ldo2>;
	status = "okay";

	button-174 {
		label = "Down";
		linux,code = <KEY_DOWN>;
		channel = <0>;
		voltage = <174603>;
	};

	button-384 {
		label = "Up";
		linux,code = <KEY_UP>;
		channel = <0>;
		voltage = <384126>;
	};

	button-593 {
		label = "OK";
		linux,code = <KEY_OK>;
		channel = <0>;
		voltage = <593650>;
	};
};

&mmc0 {
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	bus-width = <4>;
	vmmc-supply = <&reg_dcdc3>;
	status = "okay";
};

&pio {
	vcc-pd-supply = <&reg_dcdc3>;
	vcc-pe-supply = <&reg_dcdc3>;
};

#include "axp209.dtsi"

&ac_power_supply {
	status = "okay";
};

&battery_power_supply {
	status = "okay";
};

&reg_dcdc2 {
	regulator-always-on;
	regulator-min-microvolt = <1250000>;
	regulator-max-microvolt = <1250000>;
	regulator-name = "vdd-sys-cpu";
};

&reg_dcdc3 {
	regulator-always-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
	regulator-name = "vdd-3v3";
};

&reg_ldo1 {
	regulator-name = "vdd-rtc";
};

&reg_ldo2 {
	regulator-always-on;
	regulator-min-microvolt = <3000000>;
	regulator-max-microvolt = <3000000>;
	regulator-name = "avcc";
};

&spi0 {
	status = "okay";

	flash@0 {
		reg = <0>;
		compatible = "jedec,spi-nor";
		spi-max-frequency = <50000000>;
	};
};

&uart1 {
	pinctrl-0 = <&uart1_pg_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	status = "okay";
};
