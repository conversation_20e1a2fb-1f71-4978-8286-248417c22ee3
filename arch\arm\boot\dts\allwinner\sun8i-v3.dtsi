// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2019 Icenowy Zheng <<EMAIL>>
 * Copyright (C) 2021 <PERSON> <<EMAIL>>
 */

#include "sun8i-v3s.dtsi"

/ {
	soc {
		i2s0: i2s@1c22000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-v3-i2s",
				     "allwinner,sun8i-h3-i2s";
			reg = <0x01c22000 0x400>;
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S0>, <&ccu CLK_I2S0>;
			clock-names = "apb", "mod";
			dmas = <&dma 3>, <&dma 3>;
			dma-names = "rx", "tx";
			pinctrl-names = "default";
			pinctrl-0 = <&i2s0_pins>;
			resets = <&ccu RST_BUS_I2S0>;
			status = "disabled";
		};
	};
};

&ccu {
	compatible = "allwinner,sun8i-v3-ccu";
};

&codec_analog {
	compatible = "allwinner,sun8i-v3-codec-analog",
		     "allwinner,sun8i-h3-codec-analog";
};

&emac {
	/delete-property/ phy-handle;
	/delete-property/ phy-mode;
};

&mdio_mux {
	external_mdio: mdio@2 {
		reg = <2>;
		#address-cells = <1>;
		#size-cells = <0>;
	};
};

&pio {
	compatible = "allwinner,sun8i-v3-pinctrl";

	i2s0_pins: i2s0-pins {
		pins = "PG10", "PG11", "PG12", "PG13";
		function = "i2s";
	};

	uart1_pg_pins: uart1-pg-pins {
		pins = "PG6", "PG7";
		function = "uart1";
	};
};
