/*
 * Copyright 2015 <PERSON>
 *
 * <PERSON> <<EMAIL>>
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "sun9i-a80.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Cubietech Cubieboard4";
	compatible = "cubietech,a80-cubieboard4", "allwinner,sun9i-a80";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			label = "cubieboard4:green:usr";
			gpios = <&pio 7 17 GPIO_ACTIVE_HIGH>; /* PH17 */
		};

		led-1 {
			label = "cubieboard4:red:usr";
			gpios = <&pio 7 6 GPIO_ACTIVE_HIGH>; /* PH6 */
		};
	};

	vga-connector {
		compatible = "vga-connector";
		label = "vga";
		ddc-i2c-bus = <&i2c3>;

		port {
			vga_con_in: endpoint {
				remote-endpoint = <&vga_dac_out>;
			};
		};
	};

	vga-dac {
		compatible = "corpro,gm7123", "adi,adv7123";
		vdd-supply = <&reg_dcdc1>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				vga_dac_in: endpoint {
					remote-endpoint = <&tcon0_out_vga>;
				};
			};

			port@1 {
				reg = <1>;

				vga_dac_out: endpoint {
					remote-endpoint = <&vga_con_in>;
				};
			};
		};
	};

	wifi_pwrseq: wifi-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&ac100_rtc 1>;
		clock-names = "ext_clock";
		/* enables internal regulator and de-asserts reset */
		reset-gpios = <&r_pio 0 2 GPIO_ACTIVE_LOW>; /* PL2 WL-PMU-EN */
	};
};

&de {
	status = "okay";
};

&gmac {
	pinctrl-names = "default";
	pinctrl-0 = <&gmac_rgmii_pins>;
	phy-handle = <&phy1>;
	phy-mode = "rgmii-id";
	phy-supply = <&reg_cldo1>;
	status = "okay";
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c3_pins>;
	status = "okay";
};

&mdio {
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
};

&mmc0 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc0_pins>;
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <4>;
	cd-gpios = <&pio 7 18 GPIO_ACTIVE_LOW>; /* PH18 */
	status = "okay";
};

&mmc1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc1_pins>;
	vmmc-supply = <&reg_dldo1>;
	vqmmc-supply = <&reg_cldo3>;
	mmc-pwrseq = <&wifi_pwrseq>;
	bus-width = <4>;
	non-removable;
	status = "okay";
};

&mmc1_pins {
	bias-pull-up;
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_dcdc1>;
	bus-width = <8>;
	non-removable;
	cap-mmc-hw-reset;
	status = "okay";
};

&mmc2_8bit_pins {
	/* Increase drive strength for DDR modes */
	drive-strength = <40>;
};

&osc32k {
	/* osc32k input is from AC100 */
	clocks = <&ac100_rtc 0>;
};

&pio {
	vcc-pa-supply = <&reg_ldo_io1>;
	vcc-pb-supply = <&reg_aldo2>;
	vcc-pc-supply = <&reg_dcdc1>;
	vcc-pd-supply = <&reg_dc1sw>;
	vcc-pe-supply = <&reg_eldo2>;
	vcc-pf-supply = <&reg_dcdc1>;
	vcc-pg-supply = <&reg_ldo_io0>;
	vcc-ph-supply = <&reg_dcdc1>;
};

&r_ir {
	status = "okay";
};

&r_pio {
	vcc-pl-supply = <&reg_dldo2>;
	vcc-pm-supply = <&reg_eldo3>;
};

&r_rsb {
	status = "okay";

	axp809: pmic@3a3 {
		reg = <0x3a3>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;

		regulators {
			reg_aldo1: aldo1 {
				/*
				 * TODO: This should be handled by the
				 * USB PHY driver.
				 */
				regulator-always-on;
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "vcc33-usbh";
			};

			reg_aldo2: aldo2 {
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc-pb-io-cam";
			};

			aldo3 {
				/* unused */
			};

			reg_dc1sw: dc1sw {
				regulator-name = "vcc-pd";
			};

			reg_dc5ldo: dc5ldo {
				regulator-always-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1100000>;
				regulator-name = "vdd-cpus-09-usbh";
			};

			reg_dcdc1: dcdc1 {
				regulator-always-on;
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "vcc-3v";
			};

			reg_dcdc2: dcdc2 {
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1100000>;
				regulator-name = "vdd-gpu";
			};

			reg_dcdc3: dcdc3 {
				regulator-always-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1100000>;
				regulator-name = "vdd-cpua";
			};

			reg_dcdc4: dcdc4 {
				regulator-always-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1100000>;
				regulator-name = "vdd-sys-usb0-hdmi";
			};

			reg_dcdc5: dcdc5 {
				regulator-always-on;
				regulator-min-microvolt = <1425000>;
				regulator-max-microvolt = <1575000>;
				regulator-name = "vcc-dram";
			};

			reg_dldo1: dldo1 {
				/*
				 * The WiFi chip supports a wide range
				 * (3.0 ~ 4.8V) of voltages, and so does
				 * this regulator (3.0 ~ 4.2V), but
				 * Allwinner SDK always sets it to 3.3V.
				 */
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc-wifi";
			};

			reg_dldo2: dldo2 {
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "vcc-pl";
			};

			reg_eldo1: eldo1 {
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-name = "vcc-dvdd-cam";
			};

			reg_eldo2: eldo2 {
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc-pe";
			};

			reg_eldo3: eldo3 {
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "vcc-pm-codec-io1";
			};

			reg_ldo_io0: ldo_io0 {
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "vcc-pg";
			};

			reg_ldo_io1: ldo_io1 {
				regulator-min-microvolt = <2500000>;
				regulator-max-microvolt = <2500000>;
				regulator-name = "vcc-pa-gmac-2v5";
			};

			reg_rtc_ldo: rtc_ldo {
				regulator-name = "vcc-rtc-vdd1v8-io";
			};

			sw {
				/* unused */
			};
		};
	};

	axp806: pmic@745 {
		compatible = "x-powers,axp806";
		reg = <0x745>;
		interrupt-parent = <&nmi_intc>;
		interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
		interrupt-controller;
		#interrupt-cells = <1>;
		bldoin-supply = <&reg_dcdce>;

		regulators {
			reg_s_aldo1: aldo1 {
				regulator-always-on;
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "avcc";
			};

			aldo2 {
				/*
				 * unused, but use a different name to
				 * avoid name clash with axp809's aldo's
				 */
				regulator-name = "s_aldo2";
			};

			aldo3 {
				/*
				 * unused, but use a different name to
				 * avoid name clash with axp809's aldo's
				 */
				regulator-name = "s_aldo3";
			};

			reg_bldo1: bldo1 {
				regulator-always-on;
				regulator-min-microvolt = <1700000>;
				regulator-max-microvolt = <1900000>;
				regulator-name = "vcc18-efuse-adc-display-csi";
			};

			reg_bldo2: bldo2 {
				regulator-always-on;
				regulator-min-microvolt = <1700000>;
				regulator-max-microvolt = <1900000>;
				regulator-name =
					"vdd18-drampll-vcc18-pll-cpvdd";
			};

			bldo3 {
				/* unused */
			};

			reg_bldo4: bldo4 {
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1300000>;
				regulator-name = "vcc12-hsic";
			};

			reg_cldo1: cldo1 {
				/*
				 * This was 3V in the original design, but
				 * 3.3V is the recommended supply voltage
				 * for the Ethernet PHY.
				 */
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				/*
				 * The PHY requires 20ms after all voltages
				 * are applied until core logic is ready and
				 * 30ms after the reset pin is de-asserted.
				 * Set a 100ms delay to account for PMIC
				 * ramp time and board traces.
				 */
				regulator-enable-ramp-delay = <100000>;
				regulator-name = "vcc-gmac-phy";
			};

			reg_cldo2: cldo2 {
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-name = "afvcc-cam";
			};

			reg_cldo3: cldo3 {
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "vcc-io-wifi-codec-io2";
			};

			reg_dcdca: dcdca {
				regulator-always-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1100000>;
				regulator-name = "vdd-cpub";
			};

			reg_dcdcd: dcdcd {
				regulator-always-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1100000>;
				regulator-name = "vdd-vpu";
			};

			reg_dcdce: dcdce {
				regulator-always-on;
				regulator-min-microvolt = <2100000>;
				regulator-max-microvolt = <2100000>;
				regulator-name = "vcc-bldo-codec-ldoin";
			};

			sw {
				/*
				 * unused, but use a different name to
				 * avoid name clash with axp809's sw
				 */
				regulator-name = "s_sw";
			};
		};
	};

	ac100: codec@e89 {
		compatible = "x-powers,ac100";
		reg = <0xe89>;

		ac100_codec: codec {
			compatible = "x-powers,ac100-codec";
			interrupt-parent = <&r_pio>;
			interrupts = <0 9 IRQ_TYPE_LEVEL_LOW>; /* PL9 */
			#clock-cells = <0>;
			clock-output-names = "4M_adda";
		};

		ac100_rtc: rtc {
			compatible = "x-powers,ac100-rtc";
			interrupt-parent = <&nmi_intc>;
			interrupts = <0 IRQ_TYPE_LEVEL_LOW>;
			clocks = <&ac100_codec>;
			#clock-cells = <1>;
			clock-output-names = "cko1_rtc",
					     "cko2_rtc",
					     "cko3_rtc";
		};
	};
};

#include "axp809.dtsi"

&tcon0 {
	pinctrl-names = "default";
	pinctrl-0 = <&lcd0_rgb888_pins>;
};

&tcon0_out {
	tcon0_out_vga: endpoint {
		remote-endpoint = <&vga_dac_in>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_ph_pins>;
	status = "okay";
};
