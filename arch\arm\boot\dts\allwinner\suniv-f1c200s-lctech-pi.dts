// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2022 Arm Ltd,
 * based on work:
 *   Copyright 2022 Icenowy Zheng <<EMAIL>>
 */

/dts-v1/;
#include "suniv-f1c100s.dtsi"

#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Lctech Pi F1C200s";
	compatible = "lctech,pi-f1c200s", "allwinner,suniv-f1c200s",
		     "allwinner,suniv-f1c100s";

	aliases {
		serial0 = &uart1;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	reg_vcc3v3: regulator-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&mmc0 {
	broken-cd;
	bus-width = <4>;
	disable-wp;
	vmmc-supply = <&reg_vcc3v3>;
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&spi0 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi0_pc_pins>;
	status = "okay";

	flash@0 {
		compatible = "spi-nand";
		reg = <0>;
		spi-max-frequency = <40000000>;
	};
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart1_pa_pins>;
	status = "okay";
};

/*
 * This is a Type-C socket, but CC1/2 are not connected, and VBUS is connected
 * to Vin, which supplies the board. Host mode works (if the board is powered
 * otherwise), but peripheral is probably the intention.
 */
&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	status = "okay";
};
