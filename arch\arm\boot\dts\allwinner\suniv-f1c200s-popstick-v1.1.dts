// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2022 Icenowy Zheng <<EMAIL>>
 */

/dts-v1/;
#include "suniv-f1c100s.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>

/ {
	model = "Popcorn Computer PopStick v1.1";
	compatible = "sourceparts,popstick-v1.1", "sourceparts,popstick",
		     "allwinner,suniv-f1c200s", "allwinner,suniv-f1c100s";

	aliases {
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led {
			function = LED_FUNCTION_STATUS;
			color = <LED_COLOR_ID_GREEN>;
			gpios = <&pio 4 6 GPIO_ACTIVE_HIGH>; /* PE6 */
			linux,default-trigger = "heartbeat";
		};
	};

	reg_vcc3v3: regulator-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};
};

&mmc0 {
	cd-gpios = <&pio 4 3 GPIO_ACTIVE_LOW>; /* PE3 */
	bus-width = <4>;
	disable-wp;
	vmmc-supply = <&reg_vcc3v3>;
	status = "okay";
};

&otg_sram {
	status = "okay";
};

&spi0 {
	pinctrl-names = "default";
	pinctrl-0 = <&spi0_pc_pins>;
	status = "okay";

	flash@0 {
		compatible = "spi-nand";
		reg = <0>;
		spi-max-frequency = <40000000>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pe_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	status = "okay";
};
