// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2018 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

#include "sunxi-bananapi-m2-plus.dtsi"

/ {
	/*
	 * Bananapi M2+ v1.2 uses a GPIO line to change the effective
	 * resistance on the CPU regulator's feedback pin.
	 */
	reg_vdd_cpux: vdd-cpux {
		compatible = "regulator-gpio";
		regulator-name = "vdd-cpux";
		regulator-type = "voltage";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1108475>;
		regulator-max-microvolt = <1308475>;
		regulator-ramp-delay = <50>; /* 4ms */
		gpios = <&r_pio 0 1 GPIO_ACTIVE_HIGH>; /* PL1 */
		gpios-states = <0x1>;
		states = <1108475 0>, <1308475 1>;
	};
};

&cpu0 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu1 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu2 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu3 {
	cpu-supply = <&reg_vdd_cpux>;
};
