// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
// Copyright (C) 2022 Arm Ltd.
/*
 * Common peripherals and configurations for MangoPi MQ-R boards.
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/leds/common.h>

/ {
	aliases {
		serial3 = &uart3;
	};

	chosen {
		stdout-path = "serial3:115200n8";
	};

	leds {
		compatible = "gpio-leds";

		led-0 {
			color = <LED_COLOR_ID_BLUE>;
			function = LED_FUNCTION_STATUS;
			gpios = <&pio 3 22 GPIO_ACTIVE_LOW>; /* PD22 */
		};
	};

	/* board wide 5V supply directly from the USB-C socket */
	reg_vcc5v: regulator-5v {
		compatible = "regulator-fixed";
		regulator-name = "vcc-5v";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	/* SY8008 DC/DC regulator on the board */
	reg_3v3: regulator-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc-3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&reg_vcc5v>;
	};

	/* SY8008 DC/DC regulator on the board, also supplying VDD-SYS */
	reg_vcc_core: regulator-core {
		compatible = "regulator-fixed";
		regulator-name = "vcc-core";
		regulator-min-microvolt = <880000>;
		regulator-max-microvolt = <880000>;
		vin-supply = <&reg_vcc5v>;
	};

	/* XC6206 LDO on the board */
	reg_avdd2v8: regulator-avdd {
		compatible = "regulator-fixed";
		regulator-name = "avdd2v8";
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		vin-supply = <&reg_3v3>;
	};

	wifi_pwrseq: wifi-pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pio 6 12 GPIO_ACTIVE_LOW>; /* PG12 */
	};
};

&dcxo {
	clock-frequency = <24000000>;
};

&ehci1 {
	status = "okay";
};

&mmc0 {
	pinctrl-0 = <&mmc0_pins>;
	pinctrl-names = "default";
	vmmc-supply = <&reg_3v3>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>;
	disable-wp;
	bus-width = <4>;
	status = "okay";
};

&mmc1 {
	pinctrl-0 = <&mmc1_pins>;
	pinctrl-names = "default";
	vmmc-supply = <&reg_3v3>;
	non-removable;
	bus-width = <4>;
	mmc-pwrseq = <&wifi_pwrseq>;
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&pio {
	vcc-pb-supply = <&reg_3v3>;
	vcc-pd-supply = <&reg_3v3>;
	vcc-pe-supply = <&reg_avdd2v8>;
	vcc-pf-supply = <&reg_3v3>;
	vcc-pg-supply = <&reg_3v3>;
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart3_pb_pins>;
	status = "okay";
};

/* The USB-C socket has its CC pins pulled to GND, so is hardwired as a UFP. */
&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	usb1_vbus-supply = <&reg_vcc5v>;
	status = "okay";
};
