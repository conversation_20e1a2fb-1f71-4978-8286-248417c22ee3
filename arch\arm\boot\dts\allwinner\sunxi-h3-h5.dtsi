/*
 * Copyright (C) 2015 <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License as
 *     published by the Free Software Foundation; either version 2 of the
 *     License, or (at your option) any later version.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

#include <dt-bindings/clock/sun6i-rtc.h>
#include <dt-bindings/clock/sun8i-de2.h>
#include <dt-bindings/clock/sun8i-h3-ccu.h>
#include <dt-bindings/clock/sun8i-r-ccu.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/reset/sun8i-de2.h>
#include <dt-bindings/reset/sun8i-h3-ccu.h>
#include <dt-bindings/reset/sun8i-r-ccu.h>

/ {
	interrupt-parent = <&gic>;
	#address-cells = <1>;
	#size-cells = <1>;

	chosen {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		framebuffer-hdmi {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "mixer0-lcd0-hdmi";
			clocks = <&display_clocks CLK_MIXER0>,
				 <&ccu CLK_TCON0>, <&ccu CLK_HDMI>;
			status = "disabled";
		};

		framebuffer-tve {
			compatible = "allwinner,simple-framebuffer",
				     "simple-framebuffer";
			allwinner,pipeline = "mixer1-lcd1-tve";
			clocks = <&display_clocks CLK_MIXER1>,
				 <&ccu CLK_TVE>;
			status = "disabled";
		};
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: osc24M_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-accuracy = <50000>;
			clock-output-names = "osc24M";
		};

		osc32k: osc32k_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <32768>;
			clock-accuracy = <50000>;
			clock-output-names = "ext_osc32k";
		};
	};

	de: display-engine {
		compatible = "allwinner,sun8i-h3-display-engine";
		allwinner,pipelines = <&mixer0>;
		status = "disabled";
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		dma-ranges;
		ranges;

		display_clocks: clock@1000000 {
			/* compatible is in per SoC .dtsi file */
			reg = <0x01000000 0x10000>;
			clocks = <&ccu CLK_BUS_DE>,
				 <&ccu CLK_DE>;
			clock-names = "bus",
				      "mod";
			resets = <&ccu RST_BUS_DE>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		mixer0: mixer@1100000 {
			compatible = "allwinner,sun8i-h3-de2-mixer-0";
			reg = <0x01100000 0x100000>;
			clocks = <&display_clocks CLK_BUS_MIXER0>,
				 <&display_clocks CLK_MIXER0>;
			clock-names = "bus",
				      "mod";
			resets = <&display_clocks RST_MIXER0>;

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				mixer0_out: port@1 {
					reg = <1>;

					mixer0_out_tcon0: endpoint {
						remote-endpoint = <&tcon0_in_mixer0>;
					};
				};
			};
		};

		dma: dma-controller@1c02000 {
			compatible = "allwinner,sun8i-h3-dma";
			reg = <0x01c02000 0x1000>;
			interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_DMA>;
			resets = <&ccu RST_BUS_DMA>;
			#dma-cells = <1>;
		};

		tcon0: lcd-controller@1c0c000 {
			compatible = "allwinner,sun8i-h3-tcon-tv",
				     "allwinner,sun8i-a83t-tcon-tv";
			reg = <0x01c0c000 0x1000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_TCON0>, <&ccu CLK_TCON0>;
			clock-names = "ahb", "tcon-ch1";
			resets = <&ccu RST_BUS_TCON0>;
			reset-names = "lcd";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				tcon0_in: port@0 {
					reg = <0>;

					tcon0_in_mixer0: endpoint {
						remote-endpoint = <&mixer0_out_tcon0>;
					};
				};

				tcon0_out: port@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;

					tcon0_out_hdmi: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&hdmi_in_tcon0>;
					};
				};
			};
		};

		mmc0: mmc@1c0f000 {
			/* compatible and clocks are in per SoC .dtsi file */
			reg = <0x01c0f000 0x1000>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc0_pins>;
			resets = <&ccu RST_BUS_MMC0>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 60 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc1: mmc@1c10000 {
			/* compatible and clocks are in per SoC .dtsi file */
			reg = <0x01c10000 0x1000>;
			pinctrl-names = "default";
			pinctrl-0 = <&mmc1_pins>;
			resets = <&ccu RST_BUS_MMC1>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		mmc2: mmc@1c11000 {
			/* compatible and clocks are in per SoC .dtsi file */
			reg = <0x01c11000 0x1000>;
			resets = <&ccu RST_BUS_MMC2>;
			reset-names = "ahb";
			interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		sid: eeprom@1c14000 {
			/* compatible is in per SoC .dtsi file */
			reg = <0x1c14000 0x400>;
			#address-cells = <1>;
			#size-cells = <1>;

			ths_calibration: thermal-sensor-calibration@34 {
				reg = <0x34 4>;
			};
		};

		msgbox: mailbox@1c17000 {
			compatible = "allwinner,sun8i-h3-msgbox",
				     "allwinner,sun6i-a31-msgbox";
			reg = <0x01c17000 0x1000>;
			clocks = <&ccu CLK_BUS_MSGBOX>;
			resets = <&ccu RST_BUS_MSGBOX>;
			interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <1>;
		};

		usb_otg: usb@1c19000 {
			compatible = "allwinner,sun8i-h3-musb";
			reg = <0x01c19000 0x400>;
			clocks = <&ccu CLK_BUS_OTG>;
			resets = <&ccu RST_BUS_OTG>;
			interrupts = <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "mc";
			phys = <&usbphy 0>;
			phy-names = "usb";
			extcon = <&usbphy 0>;
			dr_mode = "otg";
			status = "disabled";
		};

		usbphy: phy@1c19400 {
			compatible = "allwinner,sun8i-h3-usb-phy";
			reg = <0x01c19400 0x2c>,
			      <0x01c1a800 0x4>,
			      <0x01c1b800 0x4>,
			      <0x01c1c800 0x4>,
			      <0x01c1d800 0x4>;
			reg-names = "phy_ctrl",
				    "pmu0",
				    "pmu1",
				    "pmu2",
				    "pmu3";
			clocks = <&ccu CLK_USB_PHY0>,
				 <&ccu CLK_USB_PHY1>,
				 <&ccu CLK_USB_PHY2>,
				 <&ccu CLK_USB_PHY3>;
			clock-names = "usb0_phy",
				      "usb1_phy",
				      "usb2_phy",
				      "usb3_phy";
			resets = <&ccu RST_USB_PHY0>,
				 <&ccu RST_USB_PHY1>,
				 <&ccu RST_USB_PHY2>,
				 <&ccu RST_USB_PHY3>;
			reset-names = "usb0_reset",
				      "usb1_reset",
				      "usb2_reset",
				      "usb3_reset";
			status = "disabled";
			#phy-cells = <1>;
		};

		ehci0: usb@1c1a000 {
			compatible = "allwinner,sun8i-h3-ehci", "generic-ehci";
			reg = <0x01c1a000 0x100>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI0>, <&ccu CLK_BUS_OHCI0>;
			resets = <&ccu RST_BUS_EHCI0>, <&ccu RST_BUS_OHCI0>;
			phys = <&usbphy 0>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci0: usb@1c1a400 {
			compatible = "allwinner,sun8i-h3-ohci", "generic-ohci";
			reg = <0x01c1a400 0x100>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI0>, <&ccu CLK_BUS_OHCI0>,
				 <&ccu CLK_USB_OHCI0>;
			resets = <&ccu RST_BUS_EHCI0>, <&ccu RST_BUS_OHCI0>;
			phys = <&usbphy 0>;
			phy-names = "usb";
			status = "disabled";
		};

		ehci1: usb@1c1b000 {
			compatible = "allwinner,sun8i-h3-ehci", "generic-ehci";
			reg = <0x01c1b000 0x100>;
			interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI1>, <&ccu CLK_BUS_OHCI1>;
			resets = <&ccu RST_BUS_EHCI1>, <&ccu RST_BUS_OHCI1>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci1: usb@1c1b400 {
			compatible = "allwinner,sun8i-h3-ohci", "generic-ohci";
			reg = <0x01c1b400 0x100>;
			interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI1>, <&ccu CLK_BUS_OHCI1>,
				 <&ccu CLK_USB_OHCI1>;
			resets = <&ccu RST_BUS_EHCI1>, <&ccu RST_BUS_OHCI1>;
			phys = <&usbphy 1>;
			phy-names = "usb";
			status = "disabled";
		};

		ehci2: usb@1c1c000 {
			compatible = "allwinner,sun8i-h3-ehci", "generic-ehci";
			reg = <0x01c1c000 0x100>;
			interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI2>, <&ccu CLK_BUS_OHCI2>;
			resets = <&ccu RST_BUS_EHCI2>, <&ccu RST_BUS_OHCI2>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci2: usb@1c1c400 {
			compatible = "allwinner,sun8i-h3-ohci", "generic-ohci";
			reg = <0x01c1c400 0x100>;
			interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI2>, <&ccu CLK_BUS_OHCI2>,
				 <&ccu CLK_USB_OHCI2>;
			resets = <&ccu RST_BUS_EHCI2>, <&ccu RST_BUS_OHCI2>;
			phys = <&usbphy 2>;
			phy-names = "usb";
			status = "disabled";
		};

		ehci3: usb@1c1d000 {
			compatible = "allwinner,sun8i-h3-ehci", "generic-ehci";
			reg = <0x01c1d000 0x100>;
			interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI3>, <&ccu CLK_BUS_OHCI3>;
			resets = <&ccu RST_BUS_EHCI3>, <&ccu RST_BUS_OHCI3>;
			phys = <&usbphy 3>;
			phy-names = "usb";
			status = "disabled";
		};

		ohci3: usb@1c1d400 {
			compatible = "allwinner,sun8i-h3-ohci", "generic-ohci";
			reg = <0x01c1d400 0x100>;
			interrupts = <GIC_SPI 79 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_EHCI3>, <&ccu CLK_BUS_OHCI3>,
				 <&ccu CLK_USB_OHCI3>;
			resets = <&ccu RST_BUS_EHCI3>, <&ccu RST_BUS_OHCI3>;
			phys = <&usbphy 3>;
			phy-names = "usb";
			status = "disabled";
		};

		ccu: clock@1c20000 {
			/* compatible is in per SoC .dtsi file */
			reg = <0x01c20000 0x400>;
			clocks = <&osc24M>, <&rtc CLK_OSC32K>;
			clock-names = "hosc", "losc";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		pio: pinctrl@1c20800 {
			/* compatible is in per SoC .dtsi file */
			reg = <0x01c20800 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_PIO>, <&osc24M>,
				 <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			#gpio-cells = <3>;
			interrupt-controller;
			#interrupt-cells = <3>;

			csi_pins: csi-pins {
				pins = "PE0", "PE2", "PE3", "PE4", "PE5",
				       "PE6", "PE7", "PE8", "PE9", "PE10",
				       "PE11";
				function = "csi";
			};

			emac_rgmii_pins: emac-rgmii-pins {
				pins = "PD0", "PD1", "PD2", "PD3", "PD4",
				       "PD5", "PD7", "PD8", "PD9", "PD10",
				       "PD12", "PD13", "PD15", "PD16", "PD17";
				function = "emac";
				drive-strength = <40>;
			};

			i2c0_pins: i2c0-pins {
				pins = "PA11", "PA12";
				function = "i2c0";
			};

			i2c1_pins: i2c1-pins {
				pins = "PA18", "PA19";
				function = "i2c1";
			};

			i2c2_pins: i2c2-pins {
				pins = "PE12", "PE13";
				function = "i2c2";
			};

			mmc0_pins: mmc0-pins {
				pins = "PF0", "PF1", "PF2", "PF3",
				       "PF4", "PF5";
				function = "mmc0";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc1_pins: mmc1-pins {
				pins = "PG0", "PG1", "PG2", "PG3",
				       "PG4", "PG5";
				function = "mmc1";
				drive-strength = <30>;
				bias-pull-up;
			};

			mmc2_8bit_pins: mmc2-8bit-pins {
				pins = "PC5", "PC6", "PC8",
				       "PC9", "PC10", "PC11",
				       "PC12", "PC13", "PC14",
				       "PC15", "PC16";
				function = "mmc2";
				drive-strength = <30>;
				bias-pull-up;
			};

			spdif_tx_pin: spdif-tx-pin {
				pins = "PA17";
				function = "spdif";
			};

			spi0_pins: spi0-pins {
				pins = "PC0", "PC1", "PC2", "PC3";
				function = "spi0";
			};

			spi1_pins: spi1-pins {
				pins = "PA15", "PA16", "PA14", "PA13";
				function = "spi1";
			};

			uart0_pa_pins: uart0-pa-pins {
				pins = "PA4", "PA5";
				function = "uart0";
			};

			uart1_pins: uart1-pins {
				pins = "PG6", "PG7";
				function = "uart1";
			};

			uart1_rts_cts_pins: uart1-rts-cts-pins {
				pins = "PG8", "PG9";
				function = "uart1";
			};

			uart2_pins: uart2-pins {
				pins = "PA0", "PA1";
				function = "uart2";
			};

			uart2_rts_cts_pins: uart2-rts-cts-pins {
				pins = "PA2", "PA3";
				function = "uart2";
			};

			uart3_pins: uart3-pins {
				pins = "PA13", "PA14";
				function = "uart3";
			};

			uart3_rts_cts_pins: uart3-rts-cts-pins {
				pins = "PA15", "PA16";
				function = "uart3";
			};
		};

		timer@1c20c00 {
			compatible = "allwinner,sun8i-a23-timer";
			reg = <0x01c20c00 0xa0>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		emac: ethernet@1c30000 {
			compatible = "allwinner,sun8i-h3-emac";
			syscon = <&syscon>;
			reg = <0x01c30000 0x10000>;
			interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq";
			resets = <&ccu RST_BUS_EMAC>;
			reset-names = "stmmaceth";
			clocks = <&ccu CLK_BUS_EMAC>;
			clock-names = "stmmaceth";
			status = "disabled";

			mdio: mdio {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,dwmac-mdio";
			};

			mdio-mux {
				compatible = "allwinner,sun8i-h3-mdio-mux";
				#address-cells = <1>;
				#size-cells = <0>;

				mdio-parent-bus = <&mdio>;
				/* Only one MDIO is usable at the time */
				internal_mdio: mdio@1 {
					compatible = "allwinner,sun8i-h3-mdio-internal";
					reg = <1>;
					#address-cells = <1>;
					#size-cells = <0>;

					int_mii_phy: ethernet-phy@1 {
						compatible = "ethernet-phy-ieee802.3-c22";
						reg = <1>;
						clocks = <&ccu CLK_BUS_EPHY>;
						resets = <&ccu RST_BUS_EPHY>;
					};
				};

				external_mdio: mdio@2 {
					reg = <2>;
					#address-cells = <1>;
					#size-cells = <0>;
				};
			};
		};

		mbus: dram-controller@1c62000 {
			/* compatible is in per SoC .dtsi file */
			reg = <0x01c62000 0x1000>,
			      <0x01c63000 0x1000>;
			reg-names = "mbus", "dram";
			clocks = <&ccu CLK_MBUS>,
				 <&ccu CLK_DRAM>,
				 <&ccu CLK_BUS_DRAM>;
			clock-names = "mbus", "dram", "bus";
			#address-cells = <1>;
			#size-cells = <1>;
			dma-ranges = <0x00000000 0x40000000 0xc0000000>;
			#interconnect-cells = <1>;
		};

		spi0: spi@1c68000 {
			compatible = "allwinner,sun8i-h3-spi";
			reg = <0x01c68000 0x1000>;
			interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPI0>, <&ccu CLK_SPI0>;
			clock-names = "ahb", "mod";
			dmas = <&dma 23>, <&dma 23>;
			dma-names = "rx", "tx";
			pinctrl-names = "default";
			pinctrl-0 = <&spi0_pins>;
			resets = <&ccu RST_BUS_SPI0>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		spi1: spi@1c69000 {
			compatible = "allwinner,sun8i-h3-spi";
			reg = <0x01c69000 0x1000>;
			interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPI1>, <&ccu CLK_SPI1>;
			clock-names = "ahb", "mod";
			dmas = <&dma 24>, <&dma 24>;
			dma-names = "rx", "tx";
			pinctrl-names = "default";
			pinctrl-0 = <&spi1_pins>;
			resets = <&ccu RST_BUS_SPI1>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		wdt0: watchdog@1c20ca0 {
			compatible = "allwinner,sun6i-a31-wdt";
			reg = <0x01c20ca0 0x20>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&osc24M>;
		};

		spdif: spdif@1c21000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-h3-spdif";
			reg = <0x01c21000 0x400>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_SPDIF>, <&ccu CLK_SPDIF>;
			resets = <&ccu RST_BUS_SPDIF>;
			clock-names = "apb", "spdif";
			dmas = <&dma 2>;
			dma-names = "tx";
			status = "disabled";
		};

		pwm: pwm@1c21400 {
			compatible = "allwinner,sun8i-h3-pwm";
			reg = <0x01c21400 0x8>;
			clocks = <&osc24M>;
			#pwm-cells = <3>;
			status = "disabled";
		};

		i2s0: i2s@1c22000 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-h3-i2s";
			reg = <0x01c22000 0x400>;
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S0>, <&ccu CLK_I2S0>;
			clock-names = "apb", "mod";
			dmas = <&dma 3>, <&dma 3>;
			resets = <&ccu RST_BUS_I2S0>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s1: i2s@1c22400 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-h3-i2s";
			reg = <0x01c22400 0x400>;
			interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S1>, <&ccu CLK_I2S1>;
			clock-names = "apb", "mod";
			dmas = <&dma 4>, <&dma 4>;
			resets = <&ccu RST_BUS_I2S1>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		i2s2: i2s@1c22800 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-h3-i2s";
			reg = <0x01c22800 0x400>;
			interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2S2>, <&ccu CLK_I2S2>;
			clock-names = "apb", "mod";
			dmas = <&dma 27>;
			resets = <&ccu RST_BUS_I2S2>;
			dma-names = "tx";
			status = "disabled";
		};

		codec: codec@1c22c00 {
			#sound-dai-cells = <0>;
			compatible = "allwinner,sun8i-h3-codec";
			reg = <0x01c22c00 0x400>;
			interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CODEC>, <&ccu CLK_AC_DIG>;
			clock-names = "apb", "codec";
			resets = <&ccu RST_BUS_CODEC>;
			dmas = <&dma 15>, <&dma 15>;
			dma-names = "rx", "tx";
			allwinner,codec-analog-controls = <&codec_analog>;
			status = "disabled";
		};

		uart0: serial@1c28000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28000 0x400>;
			interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART0>;
			resets = <&ccu RST_BUS_UART0>;
			dmas = <&dma 6>, <&dma 6>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart1: serial@1c28400 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28400 0x400>;
			interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART1>;
			resets = <&ccu RST_BUS_UART1>;
			dmas = <&dma 7>, <&dma 7>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart2: serial@1c28800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28800 0x400>;
			interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART2>;
			resets = <&ccu RST_BUS_UART2>;
			dmas = <&dma 8>, <&dma 8>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		uart3: serial@1c28c00 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01c28c00 0x400>;
			interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&ccu CLK_BUS_UART3>;
			resets = <&ccu RST_BUS_UART3>;
			dmas = <&dma 9>, <&dma 9>;
			dma-names = "tx", "rx";
			status = "disabled";
		};

		i2c0: i2c@1c2ac00 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2ac00 0x400>;
			interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C0>;
			resets = <&ccu RST_BUS_I2C0>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c1: i2c@1c2b000 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b000 0x400>;
			interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C1>;
			resets = <&ccu RST_BUS_I2C1>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		i2c2: i2c@1c2b400 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01c2b400 0x400>;
			interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_I2C2>;
			resets = <&ccu RST_BUS_I2C2>;
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2_pins>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		gic: interrupt-controller@1c81000 {
			compatible = "arm,gic-400";
			reg = <0x01c81000 0x1000>,
			      <0x01c82000 0x2000>,
			      <0x01c84000 0x2000>,
			      <0x01c86000 0x2000>;
			interrupt-controller;
			#interrupt-cells = <3>;
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		};

		csi: camera@1cb0000 {
			compatible = "allwinner,sun8i-h3-csi";
			reg = <0x01cb0000 0x1000>;
			interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_CSI>,
				 <&ccu CLK_CSI_SCLK>,
				 <&ccu CLK_DRAM_CSI>;
			clock-names = "bus", "mod", "ram";
			resets = <&ccu RST_BUS_CSI>;
			pinctrl-names = "default";
			pinctrl-0 = <&csi_pins>;
			status = "disabled";
		};

		hdmi: hdmi@1ee0000 {
			compatible = "allwinner,sun8i-h3-dw-hdmi",
				     "allwinner,sun8i-a83t-dw-hdmi";
			reg = <0x01ee0000 0x10000>;
			reg-io-width = <1>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&ccu CLK_BUS_HDMI>, <&ccu CLK_HDMI_DDC>,
				 <&ccu CLK_HDMI>, <&rtc CLK_OSC32K>;
			clock-names = "iahb", "isfr", "tmds", "cec";
			resets = <&ccu RST_BUS_HDMI1>;
			reset-names = "ctrl";
			phys = <&hdmi_phy>;
			phy-names = "phy";
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi_in: port@0 {
					reg = <0>;

					hdmi_in_tcon0: endpoint {
						remote-endpoint = <&tcon0_out_hdmi>;
					};
				};

				hdmi_out: port@1 {
					reg = <1>;
				};
			};
		};

		hdmi_phy: hdmi-phy@1ef0000 {
			compatible = "allwinner,sun8i-h3-hdmi-phy";
			reg = <0x01ef0000 0x10000>;
			clocks = <&ccu CLK_BUS_HDMI>, <&ccu CLK_HDMI_DDC>,
				 <&ccu CLK_PLL_VIDEO>;
			clock-names = "bus", "mod", "pll-0";
			resets = <&ccu RST_BUS_HDMI0>;
			reset-names = "phy";
			#phy-cells = <0>;
		};

		rtc: rtc@1f00000 {
			/* compatible is in per SoC .dtsi file */
			reg = <0x01f00000 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
			clock-output-names = "osc32k", "osc32k-out", "iosc";
			clocks = <&osc32k>;
			#clock-cells = <1>;
		};

		r_intc: interrupt-controller@1f00c00 {
			compatible = "allwinner,sun8i-h3-r-intc",
				     "allwinner,sun6i-a31-r-intc";
			interrupt-controller;
			#interrupt-cells = <3>;
			reg = <0x01f00c00 0x400>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
		};

		r_ccu: clock@1f01400 {
			compatible = "allwinner,sun8i-h3-r-ccu";
			reg = <0x01f01400 0x100>;
			clocks = <&osc24M>, <&rtc CLK_OSC32K>, <&rtc CLK_IOSC>,
				 <&ccu CLK_PLL_PERIPH0>;
			clock-names = "hosc", "losc", "iosc", "pll-periph";
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		codec_analog: codec-analog@1f015c0 {
			compatible = "allwinner,sun8i-h3-codec-analog";
			reg = <0x01f015c0 0x4>;
		};

		ir: ir@1f02000 {
			compatible = "allwinner,sun6i-a31-ir";
			clocks = <&r_ccu CLK_APB0_IR>, <&r_ccu CLK_IR>;
			clock-names = "apb", "ir";
			resets = <&r_ccu RST_APB0_IR>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			reg = <0x01f02000 0x400>;
			status = "disabled";
		};

		r_i2c: i2c@1f02400 {
			compatible = "allwinner,sun6i-a31-i2c";
			reg = <0x01f02400 0x400>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_i2c_pins>;
			clocks = <&r_ccu CLK_APB0_I2C>;
			resets = <&r_ccu RST_APB0_I2C>;
			status = "disabled";
			#address-cells = <1>;
			#size-cells = <0>;
		};

		r_uart: serial@1f02800 {
			compatible = "snps,dw-apb-uart";
			reg = <0x01f02800 0x400>;
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			reg-shift = <2>;
			reg-io-width = <4>;
			clocks = <&r_ccu CLK_APB0_UART>;
			resets = <&r_ccu RST_APB0_UART>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_uart_pins>;
			status = "disabled";
		};

		r_pio: pinctrl@1f02c00 {
			compatible = "allwinner,sun8i-h3-r-pinctrl";
			reg = <0x01f02c00 0x400>;
			interrupt-parent = <&r_intc>;
			interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&r_ccu CLK_APB0_PIO>, <&osc24M>,
				 <&rtc CLK_OSC32K>;
			clock-names = "apb", "hosc", "losc";
			gpio-controller;
			#gpio-cells = <3>;
			interrupt-controller;
			#interrupt-cells = <3>;

			r_ir_rx_pin: r-ir-rx-pin {
				pins = "PL11";
				function = "s_cir_rx";
			};

			r_i2c_pins: r-i2c-pins {
				pins = "PL0", "PL1";
				function = "s_i2c";
			};

			r_pwm_pin: r-pwm-pin {
				pins = "PL10";
				function = "s_pwm";
			};

			r_uart_pins: r-uart-pins {
				pins = "PL2", "PL3";
				function = "s_uart";
			};
		};

		r_pwm: pwm@1f03800 {
			compatible = "allwinner,sun8i-h3-pwm";
			reg = <0x01f03800 0x8>;
			pinctrl-names = "default";
			pinctrl-0 = <&r_pwm_pin>;
			clocks = <&osc24M>;
			#pwm-cells = <3>;
			status = "disabled";
		};
	};
};
