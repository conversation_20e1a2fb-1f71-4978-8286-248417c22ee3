// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (C) 2017 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	aliases {
		ethernet0 = &emac;
		serial0 = &uart0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	connector {
		compatible = "hdmi-connector";
		type = "a";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";

		pwr_led {
			label = "librecomputer:green:pwr";
			gpios = <&r_pio 0 10 GPIO_ACTIVE_HIGH>; /* PL10 */
			default-state = "on";
		};

		status_led {
			label = "librecomputer:blue:status";
			gpios = <&pio 0 7 GPIO_ACTIVE_HIGH>; /* PA7 */
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-power {
			label = "power";
			linux,code = <KEY_POWER>;
			gpios = <&r_pio 0 2 GPIO_ACTIVE_LOW>; /* PL2 */
			wakeup-source;
		};
	};

	reg_vcc1v2: vcc1v2 {
		compatible = "regulator-fixed";
		regulator-name = "vcc1v2";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc5v0>;
		gpio = <&r_pio 0 8 GPIO_ACTIVE_HIGH>; /* PL8 */
		enable-active-high;
	};

	reg_vcc3v3: vcc3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&reg_vcc5v0>;
	};

	/* This represents the board's 5V input */
	reg_vcc5v0: vcc5v0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	reg_vcc_dram: vcc-dram {
		compatible = "regulator-fixed";
		regulator-name = "vcc-dram";
		regulator-min-microvolt = <1500000>;
		regulator-max-microvolt = <1500000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc5v0>;
		gpio = <&r_pio 0 9 GPIO_ACTIVE_HIGH>; /* PL9 */
		enable-active-high;
	};

	reg_vcc_io: vcc-io {
		compatible = "regulator-fixed";
		regulator-name = "vcc-io";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc3v3>;
		gpio = <&r_pio 0 5 GPIO_ACTIVE_LOW>; /* PL5 */
	};

	reg_vdd_cpux: vdd-cpux {
		compatible = "regulator-fixed";
		regulator-name = "vdd-cpux";
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc5v0>;
		gpio = <&r_pio 0 8 GPIO_ACTIVE_HIGH>; /* PL8 */
		enable-active-high;
	};
};

&codec {
	allwinner,audio-routing =
		"Line Out", "LINEOUT",
		"MIC1", "Mic",
		"Mic",  "MBIAS";
	status = "okay";
};

&cpu0 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu1 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu2 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu3 {
	cpu-supply = <&reg_vdd_cpux>;
};

&de {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ehci2 {
	status = "okay";
};

&ehci3 {
	status = "okay";
};

&emac {
	phy-handle = <&int_mii_phy>;
	phy-mode = "mii";
	allwinner,leds-active-low;
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&ir {
	pinctrl-names = "default";
	pinctrl-0 = <&r_ir_rx_pin>;
	status = "okay";
};

&mmc0 {
	vmmc-supply = <&reg_vcc_io>;
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	status = "okay";
};

&mmc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&mmc2_8bit_pins>;
	vmmc-supply = <&reg_vcc_io>;
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&ohci2 {
	status = "okay";
};

&ohci3 {
	status = "okay";
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pa_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "host";
	status = "okay";
};

&usbphy {
	/* VBUS on USB ports are always on */
	usb0_vbus-supply = <&reg_vcc5v0>;
	usb1_vbus-supply = <&reg_vcc5v0>;
	usb2_vbus-supply = <&reg_vcc5v0>;
	usb3_vbus-supply = <&reg_vcc5v0>;
	status = "okay";
};
