// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
// Copyright (C) 2019 <PERSON><PERSON><PERSON> <<EMAIL>>

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>

/ {
	aliases {
		serial0 = &uart0;
		spi0 = &spi0;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	connector {
		compatible = "hdmi-connector";
		type = "d";

		port {
			hdmi_con_in: endpoint {
				remote-endpoint = <&hdmi_out_con>;
			};
		};
	};

	leds {
		compatible = "gpio-leds";

		status_led {
			label = "librecomputer:blue:status";
			gpios = <&pio 0 7 GPIO_ACTIVE_HIGH>; /* PA7 */
		};
	};

	reg_vcc3v3: vcc3v3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc5v0>;
	};

	/* This represents the board's 5V input */
	reg_vcc5v0: vcc5v0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	reg_vcc_dram: vcc-dram {
		compatible = "regulator-fixed";
		regulator-name = "vcc-dram";
		regulator-min-microvolt = <1500000>;
		regulator-max-microvolt = <1500000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc5v0>;
		gpio = <&r_pio 0 9 GPIO_ACTIVE_HIGH>; /* PL9 */
		enable-active-high;
	};

	reg_vcc_io: vcc-io {
		compatible = "regulator-fixed";
		regulator-name = "vcc-io";
		/* This is simply a MOSFET switch */
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc3v3>;
		gpio = <&r_pio 0 5 GPIO_ACTIVE_LOW>; /* PL5 */
	};

	reg_vcc_usbwifi: vcc-usbwifi {
		compatible = "regulator-fixed";
		regulator-name = "vcc-usbwifi";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&reg_vcc5v0>;
		gpio = <&pio 6 4 GPIO_ACTIVE_HIGH>; /* PG4 */
		enable-active-high;
	};

	reg_vdd_cpux: vdd-cpux {
		compatible = "regulator-fixed";
		regulator-name = "vdd-cpux";
		regulator-min-microvolt = <1100000>;
		regulator-max-microvolt = <1100000>;
		regulator-always-on;
		regulator-boot-on;
		vin-supply = <&reg_vcc5v0>;
		gpio = <&r_pio 0 8 GPIO_ACTIVE_HIGH>; /* PL8 */
		enable-active-high;
	};
};

&cpu0 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu1 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu2 {
	cpu-supply = <&reg_vdd_cpux>;
};

&cpu3 {
	cpu-supply = <&reg_vdd_cpux>;
};

&de {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmi_out {
	hdmi_out_con: endpoint {
		remote-endpoint = <&hdmi_con_in>;
	};
};

&mmc0 {
	vmmc-supply = <&reg_vcc_io>;
	bus-width = <4>;
	cd-gpios = <&pio 5 6 GPIO_ACTIVE_LOW>; /* PF6 */
	status = "okay";
};

&pio {
	vcc-pa-supply = <&reg_vcc_io>;
	vcc-pc-supply = <&reg_vcc_io>;
	vcc-pd-supply = <&reg_vcc_io>;
	vcc-pe-supply = <&reg_vcc_io>;
	vcc-pf-supply = <&reg_vcc_io>;
	vcc-pg-supply = <&reg_vcc_io>;
};

&r_pio {
	vcc-pl-supply = <&reg_vcc3v3>;
};

&spi0 {
	status = "okay";

	flash@0 {
		compatible = "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <50000000>;
	};
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart0_pa_pins>;
	status = "okay";
};

&usb_otg {
	dr_mode = "peripheral";
	status = "okay";
};

&usbphy {
	usb1_vbus-supply = <&reg_vcc_usbwifi>;
	status = "okay";
};
